{"很遗憾您这没有能用的显卡来支持您训练": "죄송합니다. 훈련을 지원할 수 있는 그래픽 카드가 없습니다.", "UVR5已开启": "UVR5가 활성화되었습니다", "UVR5已关闭": "UVR5가 비활성화되었습니다", "本软件以MIT协议开源, 作者不对软件具备任何控制力, 使用软件者、传播软件导出的声音者自负全责. <br>如不认可该条款, 则不能使用或引用软件包内任何代码和文件. 详见根目录<b>LICENSE</b>.": "본 소프트웨어는 MIT 라이선스로 오픈 소스로 제공되며, 제작자는 소프트웨어에 대해 어떠한 제어력도 가지지 않습니다. 소프트웨어 사용자 및 소프트웨어에서 내보낸 소리를 전파하는 자는 전적으로 책임져야 합니다. <br>이 조항을 인정하지 않으면 소프트웨어의 코드 및 파일을 사용하거나 인용할 수 없습니다. 루트 디렉터리의 <b>LICENSE</b>를 참조하십시오.", "0-前置数据集获取工具": "0-전방 데이터 세트 수집 도구", "0a-UVR5人声伴奏分离&去混响去延迟工具": "0a-UVR5 보컬 및 반주 분리 및 에코 및 지연 제거 도구", "是否开启UVR5-WebUI": "UVR5-WebUI를 여시겠습니까?", "UVR5进程输出信息": "UVR5 프로세스 출력 정보", "0b-语音切分工具": "0b-음성 분리 도구", ".list标注文件的路径": ".list 주석 파일 경로", "GPT模型路径": "GPT 모델 경로", "SoVITS模型列表": "SoVITS 모델 목록", "填切割后音频所在目录！读取的音频文件完整路径=该目录-拼接-list文件里波形对应的文件名（不是全路径）。": "분리된 오디오가 있는 디렉터리를 입력하십시오! 읽은 오디오 파일의 전체 경로 = 해당 디렉터리-연결-목록 파일에 해당하는 원본 이름 (전체 경로가 아님).", "音频自动切分输入路径，可文件可文件夹": "오디오 자동 분리 입력 경로, 파일 또는 폴더 가능", "切分后的子音频的输出根目录": "분리된 하위 오디오의 출력 기본 디렉터리", "怎么切": "자르기 옵션", "不切": "자르지 않음", "凑四句一切": "네 문장의 세트를 완성하세요.", "按英文句号.切": "영어 문장으로 분리하기", "threshold:音量小于这个值视作静音的备选切割点": "임계 값: 이 값보다 작은 볼륨은 대체 분리 지점으로 간주됩니다.", "min_length:每段最小多长，如果第一段太短一直和后面段连起来直到超过这个值": "최소 길이: 각 세그먼트의 최소 길이. 첫 번째 세그먼트가 너무 짧으면 계속해서 뒷부분과 연결하여 이 값 이상이 될 때까지", "min_interval:最短切割间隔": "최소 분리 간격", "hop_size:怎么算音量曲线，越小精度越大计算量越高（不是精度越大效果越好）": "hop 크기: 볼륨 곡선을 계산하는 방법. 작을수록 정확도가 높아지지만 계산량이 높아집니다 (정확도가 높다고 효과가 좋아지지 않음)", "max_sil_kept:切完后静音最多留多长": "최대 유지되는 정적 길이 (분리 후)", "开启语音切割": "음성 분리 활성화", "终止语音切割": "음성 분리 종료", "max:归一化后最大值多少": "최대 값 (정규화 후)", "alpha_mix:混多少比例归一化后音频进来": "알파 믹스: 정규화된 오디오가 들어오는 비율", "切割使用的进程数": "사용되는 프로세스 수로 자르기", "语音切割进程输出信息": "음성 분리 프로세스 출력 정보", "0c-中文批量离线ASR工具": "0c-중국어 대량 오프라인 ASR 도구", "开启离线批量ASR": "오프라인 대량 ASR 활성화", "终止ASR进程": "ASR 프로세스 종료", "批量ASR(中文only)输入文件夹路径": "대량 ASR (중국어 전용) 입력 폴더 경로", "ASR进程输出信息": "ASR 프로세스 출력 정보", "0d-语音文本校对标注工具": "0d-음성 텍스트 교정 주석 도구", "是否开启打标WebUI": "웹 기반 주석 활성화 여부", "打标数据标注文件路径": "주석 데이터 주석 파일 경로", "打标工具进程输出信息": "주석 도구 프로세스 출력 정보", "1-GPT-SoVITS-TTS": "1-GPT-SoVITS-TTS", "*实验/模型名": "*실험/모델 이름", "显卡信息": "그래픽 카드 정보", "预训练的SoVITS-G模型路径": "사전 훈련된 SoVITS-G 모델 경로", "预训练的SoVITS-D模型路径": "사전 훈련된 SoVITS-D 모델 경로", "预训练的GPT模型路径": "사전 훈련된 GPT 모델 경로", "1A-训练集格式化工具": "1A-훈련 세트 형식 지정 도구", "输出logs/实验名目录下应有23456开头的文件和文件夹": "logs/실험 이름 디렉터리에는 23456으로 시작하는 파일과 폴더가 있어야 함", "*文本标注文件": "*텍스트 주석 파일", "*训练集音频文件目录": "*훈련 세트 오디오 파일 디렉터리", "训练集音频文件目录 拼接 list文件里波形对应的文件名。": "훈련 세트 오디오 파일 디렉터리 - 목록 파일에 해당하는 원형 이름 연결", "1Aa-文本内容": "1Aa-텍스트 내용", "GPU卡号以-分割，每个卡号一个进程": "GPU 카드 번호는 -로 구분되며 각 카드 번호에 하나의 프로세스가 있어야 함", "预训练的中文BERT模型路径": "사전 훈련된 중국어 BERT 모델 경로", "开启文本获取": "텍스트 추출 활성화", "终止文本获取进程": "텍스트 추출 프로세스 종료", "文本进程输出信息": "텍스트 프로세스 출력 정보", "1Ab-SSL自监督特征提取": "1Ab-SSL 자기 지도 특징 추출", "预训练的SSL模型路径": "사전 훈련된 SSL 모델 경로", "开启SSL提取": "SSL 추출 활성화", "终止SSL提取进程": "SSL 추출 프로세스 종료", "SSL进程输出信息": "SSL 프로세스 출력 정보", "1Ac-语义token提取": "1Ac-의미 토큰 추출", "开启语义token提取": "의미 토큰 추출 활성화", "终止语义token提取进程": "의미 토큰 추출 프로세스 종료", "语义token提取进程输出信息": "의미 토큰 추출 프로세스 출력 정보", "1Aabc-训练集格式化一键三连": "1Aabc-훈련 세트 형식 지정 일괄 처리", "开启一键三连": "일괄 처리 활성화", "终止一键三连": "일괄 처리 종료", "一键三连进程输出信息": "일괄 처리 프로세스 출력 정보", "1B-微调训练": "1B-미세 조정 훈련", "1Ba-SoVITS训练。用于分享的模型文件输出在SoVITS_weights下。": "1Ba-SoVITS 훈련. 공유 용 모델 파일은 SoVITS_weights 하위에 출력됩니다.", "每张显卡的batch_size": "각 그래픽 카드의 배치 크기", "总训练轮数total_epoch，不建议太高": "총 훈련 라운드 수 (total_epoch), 너무 높지 않게 권장됨", "文本模块学习率权重": "텍스트 모듈 학습률 가중치", "保存频率save_every_epoch": "저장 빈도 (각 라운드마다)", "是否仅保存最新的ckpt文件以节省硬盘空间": "디스크 공간을 절약하기 위해 최신 ckpt 파일만 저장할지 여부", "是否在每次保存时间点将最终小模型保存至weights文件夹": "각 저장 시간에 최종 작은 모델을 weights 폴더에 저장할지 여부", "开启SoVITS训练": "SoVITS 훈련 활성화", "终止SoVITS训练": "SoVITS 훈련 종료", "SoVITS训练进程输出信息": "SoVITS 훈련 프로세스 출력 정보", "1Bb-GPT训练。用于分享的模型文件输出在GPT_weights下。": "1Bb-GPT 훈련. 공유 용 모델 파일은 GPT_weights 하위에 출력됩니다.", "总训练轮数total_epoch": "총 훈련 라운드 수 (total_epoch)", "开启GPT训练": "GPT 훈련 활성화", "终止GPT训练": "GPT 훈련 종료", "GPT训练进程输出信息": "GPT 훈련 프로세스 출력 정보", "1C-推理": "1C-추론", "选择训练完存放在SoVITS_weights和GPT_weights下的模型。默认的一个是底模，体验5秒Zero Shot TTS用。": "SoVITS_weights 및 GPT_weights에 저장된 훈련 완료된 모델 중 선택. 기본적으로 하나는 기본 모델이며 5초 Zero Shot TTS를 체험할 수 있습니다.", "*GPT模型列表": "*GPT 모델 목록", "*SoVITS模型列表": "*SoVITS 모델 목록", "GPU卡号,只能填1个整数": "GPU 카드 번호, 1개의 정수만 입력 가능", "刷新模型路径": "모델 경로 새로 고침", "是否开启TTS推理WebUI": "TTS 추론 WebUI 활성화 여부", "TTS推理WebUI进程输出信息": "TTS 추론 WebUI 프로세스 출력 정보", "2-GPT-SoVITS-变声": "2-GPT-SoVITS-음성 변환", "施工中，请静候佳音": "공사 중입니다. 기다려주십시오.", "参考音频在3~10秒范围外，请更换！": "참고 오디오가 3~10초 범위를 벗어났습니다. 다른 것으로 바꾸십시오!", "请上传3~10秒内参考音频，超过会报错！": "3~10초 이내의 참고 오디오를 업로드하십시오. 초과하면 오류가 발생합니다!", "TTS推理进程已开启": "TTS 추론 프로세스가 열렸습니다", "TTS推理进程已关闭": "TTS 추론 프로세스가 닫혔습니다", "打标工具WebUI已开启": "주석 도구 WebUI가 열렸습니다", "打标工具WebUI已关闭": "주석 도구 WebUI가 닫혔습니다", "*请填写需要合成的目标文本。中英混合选中文，日英混合选日文，中日混合暂不支持，非目标语言文本自动遗弃。": "*합성할 대상 텍스트를 입력하십시오. 중국어와 영어를 혼합하면 중국어를 선택하고 일본어와 영어를 혼합하면 일본어를 선택하십시오. 중국어와 일본어를 혼합하는 것은 아직 지원되지 않으며 대상 언어가 아닌 텍스트는 자동으로 버려집니다.", "*请填写需要合成的目标文本": "*합성할 대상 텍스트를 입력하십시오", "ASR任务开启：%s": "ASR 작업 시작: %s", "GPT训练完成": "GPT 훈련 완료", "GPT训练开始：%s": "GPT 훈련 시작: %s", "SSL提取进程执行中": "SSL 추출 프로세스 실행 중", "SSL提取进程结束": "SSL 추출 프로세스 종료", "SoVITS训练完成": "SoVITS 훈련 완료", "SoVITS训练开始：%s": "SoVITS 훈련 시작: %s", "一键三连中途报错": "일괄 처리 중 오류 발생", "一键三连进程结束": "일괄 처리 프로세스 종료", "中文": "중국어", "凑50字一切": "50자를 채우십시오", "凑五句一切": "다섯 문장을 채우십시오", "切分后文本": "분리된 텍스트", "切割执行中": "분리 진행 중", "切割结束": "분리 종료", "参考音频的文本": "참고 오디오의 텍스트", "参考音频的语种": "참고 오디오의 언어", "合成语音": "합성 음성", "后续将支持混合语种编码文本输入。": "향후 혼합 언어 코딩 텍스트 입력을 지원할 예정입니다.", "已有正在进行的ASR任务，需先终止才能开启下一次任务": "이미 진행 중인 ASR 작업이 있습니다. 다음 작업을 시작하려면 먼저 종료하십시오.", "已有正在进行的GPT训练任务，需先终止才能开启下一次任务": "이미 진행 중인 GPT 훈련 작업이 있습니다. 다음 작업을 시작하려면 먼저 종료하십시오.", "已有正在进行的SSL提取任务，需先终止才能开启下一次任务": "이미 진행 중인 SSL 추출 작업이 있습니다. 다음 작업을 시작하려면 먼저 종료하십시오.", "已有正在进行的SoVITS训练任务，需先终止才能开启下一次任务": "이미 진행 중인 SoVITS 훈련 작업이 있습니다. 다음 작업을 시작하려면 먼저 종료하십시오.", "已有正在进行的一键三连任务，需先终止才能开启下一次任务": "이미 진행 중인 일괄 처리 작업이 있습니다. 다음 작업을 시작하려면 먼저 종료하십시오.", "已有正在进行的切割任务，需先终止才能开启下一次任务": "이미 진행 중인 분리 작업이 있습니다. 다음 작업을 시작하려면 먼저 종료하십시오.", "已有正在进行的文本任务，需先终止才能开启下一次任务": "이미 진행 중인 텍스트 작업이 있습니다. 다음 작업을 시작하려면 먼저 종료하십시오.", "已有正在进行的语义token提取任务，需先终止才能开启下一次任务": "이미 진행 중인 의미 토큰 추출 작업이 있습니다. 다음 작업을 시작하려면 먼저 종료하십시오.", "已终止ASR进程": "ASR 프로세스 종료됨", "已终止GPT训练": "GPT 훈련 종료됨", "已终止SoVITS训练": "SoVITS 훈련 종료됨", "已终止所有1a进程": "모든 1a 프로세스 종료됨", "已终止所有1b进程": "모든 1b 프로세스 종료됨", "已终止所有一键三连进程": "모든 일괄 처리 프로세스 종료됨", "已终止所有切割进程": "모든 분리 프로세스 종료됨", "已终止所有语义token进程": "모든 의미 토큰 프로세스 종료됨", "按中文句号。切": "중국어 문장으로 분리하십시오.", "文本切分工具。太长的文本合成出来效果不一定好，所以太长建议先切。合成会根据文本的换行分开合成再拼起来。": "텍스트 분리 도구. 너무 긴 텍스트는 합성 결과가 항상 좋지 않을 수 있으므로 너무 길면 먼저 분리하는 것이 좋습니다. 합성은 텍스트 줄 바꿈을 기준으로 분리되어 다시 조합됩니다.", "文本进程执行中": "텍스트 프로세스 실행 중", "文本进程结束": "텍스트 프로세스 종료", "日文": "일본어", "英文": "영어", "语义token提取进程执行中": "의미 토큰 추출 프로세스 실행 중", "语义token提取进程结束": "의미 토큰 추출 프로세스 종료", "请上传参考音频": "참고 오디오를 업로드하십시오", "输入路径不存在": "입력 경로가 존재하지 않습니다", "输入路径存在但既不是文件也不是文件夹": "입력 경로가 파일이나 폴더가 아닙니다", "输出的语音": "출력 음성", "进度：1a-done": "진행: 1a-done", "进度：1a-done, 1b-ing": "진행: 1a-done, 1b-ing", "进度：1a-ing": "진행: 1a-ing", "进度：1a1b-done": "진행: 1a1b-done", "进度：1a1b-done, 1cing": "진행: 1a1b-done, 1cing", "进度：all-done": "진행: all-done", "需要合成的切分前文本": "합성해야 할 분할 전 텍스트", "需要合成的文本": "합성해야 할 텍스트", "需要合成的语种": "합성해야 할 언어", ">=3则使用对harvest音高识别的结果使用中值滤波，数值为滤波半径，使用可以削弱哑音": ">=3이면 harvest 음고 인식 결과에 중앙값 필터를 사용하며, 값은 필터 반경이며 사용하면 소리를 약하게 할 수 있습니다", "A模型权重": "A 모델 가중치", "A模型路径": "A 모델 경로", "B模型路径": "B 모델 경로", "E:\\语音音频+标注\\米津玄师\\src": "E:\\음성 오디오 + 주석\\<PERSON><PERSON>\\src", "F0曲线文件, 可选, 一行一个音高, 代替默认F0及升降调": "F0 곡선 파일, 선택 사항, 한 줄에 하나의 음고, 기본 F0 및 음조 대신 사용", "Index Rate": "인덱스 비율", "Onnx导出": "Onnx 내보내기", "Onnx输出路径": "Onnx 출력 경로", "RVC模型路径": "RVC 모델 경로", "ckpt处理": "ckpt 처리", "harvest进程数": "harvest 프로세스 수", "index文件路径不可包含中文": "인덱스 파일 경로에는 중국어를 포함할 수 없습니다", "pth文件路径不可包含中文": "pth 파일 경로에는 중국어를 포함할 수 없습니다", "rmvpe卡号配置：以-分隔输入使用的不同进程卡号,例如0-0-1使用在卡0上跑2个进程并在卡1上跑1个进程": "rmvpe 카드 번호 구성: 각 입력에 사용되는 다른 프로세스 카드를 -로 구분하여 입력하십시오. 예: 0-0-1은 카드 0에서 2개의 프로세스를 실행하고 카드 1에서 1개의 프로세스를 실행합니다", "step1: 填写实验配置. 实验数据放在logs下, 每个实验一个文件夹, 需手工输入实验名路径, 内含实验配置, 日志, 训练得到的模型文件. ": "step1: 실험 구성 입력. 실험 데이터는 logs 하위에 있으며 각 실험에 대한 폴더가 있어야합니다. 실험 이름 경로를 수동으로 입력해야하며 실험 구성, 로그, 훈련된 모델 파일이 포함되어 있습니다.", "step1:正在处理数据": "step1: 데이터 처리 중", "step2:正在提取音高&正在提取特征": "step2: 음고 추출 및 특징 추출 중", "step2a: 自动遍历训练文件夹下所有可解码成音频的文件并进行切片归一化, 在实验目录下生成2个wav文件夹; 暂时只支持单人训练. ": "step2a: 자동으로 훈련 폴더에서 오디오로 디코딩할 수 있는 모든 파일을 반복하고 슬라이스 정규화를 수행하여 실험 디렉토리에 2 개의 wav 폴더를 생성합니다. 현재 단일 훈련만 지원됩니다.", "step2b: 使用CPU提取音高(如果模型带音高), 使用GPU提取特征(选择卡号)": "step2b: CPU로 음고 추출(모델이 음고를 지원하는 경우), GPU로 특징 추출(카드 번호 선택)", "step3: 填写训练设置, 开始训练模型和索引": "step3: 훈련 설정 입력, 모델 및 인덱스 훈련 시작", "step3a:正在训练模型": "step3a: 모델 훈련 중", "一键训练": "일괄 훈련", "也可批量输入音频文件, 二选一, 优先读文件夹": "오디오 파일을 일괄로 입력할 수도 있습니다. 둘 중 하나를 선택하고 폴더를 읽기를 우선합니다.", "以-分隔输入使用的卡号, 例如   0-1-2   使用卡0和卡1和卡2": "-로 구분하여 입력에 사용되는 카드 번호를 지정하십시오. 예 : 0-1-2는 카드 0, 1 및 2를 사용합니다", "伴奏人声分离&去混响&去回声": "반주 및 보컬 분리 & 리버브 제거 & 에코 제거", "使用模型采样率": "모델 샘플링 속도 사용", "使用设备采样率": "기기 샘플링 속도 사용", "保存名": "저장 이름", "保存的文件名, 默认空为和源文件同名": "저장할 파일 이름, 기본적으로 공백은 원본 파일과 동일한 이름입니다", "保存的模型名不带后缀": "저장할 모델 이름에는 확장자가 없습니다", "保护清辅音和呼吸声，防止电音撕裂等artifact，拉满0.5不开启，调低加大保护力度但可能降低索引效果": "클리어 자음 및 숨소를 보호하여 전자 음향 찢김과 같은 아티팩트를 방지하려면 0.5로 설정하되, 보호 강도를 높이려면 0.5로 당기지 않고 낮추면 인덱스 효과가 감소할 수 있습니다", "修改": "수정", "修改模型信息(仅支持weights文件夹下提取的小模型文件)": "모델 정보 수정 (weights 폴더에서 추출된 작은 모델 파일만 지원됨)", "停止音频转换": "오디오 변환 중지", "全流程结束！": "전체 프로세스 완료!", "刷新音色列表和索引路径": "음색 목록 및 인덱스 경로 새로 고침", "加载模型": "모델 로드", "加载预训练底模D路径": "사전 훈련된 기본 모델 D 경로 로드", "加载预训练底模G路径": "사전 훈련된 기본 모델 G 경로 로드", "单次推理": "단일 추론", "卸载音色省显存": "음색 언로드 및 GPU 메모리 절약", "变调(整数, 半音数量, 升八度12降八度-12)": "음높이 변경(정수, 반음 수, 올림 높이 12 내림 높이 -12)", "后处理重采样至最终采样率，0为不进行重采样": "후 처리를 통한 최종 샘플링률 재샘플링, 0은 재샘플링 미실행", "否": "아니오", "启用相位声码器": "페이즈 보코더 사용", "响应阈值": "응답 임계값", "响度因子": "음량 요소", "处理数据": "데이터 처리", "导出Onnx模型": "Onnx 모델 내보내기", "导出文件格式": "내보내기 파일 형식", "常见问题解答": "자주 묻는 질문 해결", "常规设置": "일반 설정", "开始音频转换": "오디오 변환 시작", "性能设置": "성능 설정", "批量推理": "일괄 추론", "批量转换, 输入待转换音频文件夹, 或上传多个音频文件, 在指定文件夹(默认opt)下输出转换的音频. ": "일괄 변환, 변환 대기 중인 오디오 폴더를 입력하거나 여러 오디오 파일을 업로드하고 지정된 폴더(opt 기본값)에 변환된 오디오를 출력합니다.", "指定输出主人声文件夹": "지정된 주인 목소리 출력 폴더", "指定输出文件夹": "지정된 출력 폴더", "指定输出非主人声文件夹": "지정된 비주인 목소리 출력 폴더", "推理时间(ms):": "추론 시간(ms):", "推理音色": "추론 음색", "提取": "추출", "提取音高和处理数据使用的CPU进程数": "음높이 추출 및 데이터 처리에 사용되는 CPU 프로세스 수 추출", "是": "예", "是否缓存所有训练集至显存. 10min以下小数据可缓存以加速训练, 大数据缓存会炸显存也加不了多少速": "모든 훈련 세트를 GPU 메모리에 캐시할지 여부. 10분 미만의 소량 데이터는 훈련 속도를 높이기 위해 캐시할 수 있지만, 대량 데이터를 캐시하면 메모리가 터지고 속도가 크게 향상되지 않을 수 있습니다.", "查看": "보기", "查看模型信息(仅支持weights文件夹下提取的小模型文件)": "모델 정보보기(작은 모델 파일로 추출된 weights 폴더에서만 지원)", "检索特征占比": "특징 비율 검색", "模型": "모델", "模型推理": "모델 추론", "模型提取(输入logs文件夹下大文件模型路径),适用于训一半不想训了模型没有自动提取保存小文件模型,或者想测试中间模型的情况": "모델 추출(로그 폴더에 대형 파일 모델 경로 입력), 반 훈련하고 싶지 않거나 모델이 자동으로 작은 파일 모델로 추출되지 않았거나 중간 모델을 테스트하려는 경우에 사용", "模型是否带音高指导": "모델에 음높이 안내가 있는지 여부", "模型是否带音高指导(唱歌一定要, 语音可以不要)": "모델에 음높이 안내가 있는지 여부(노래에는 필수, 음성은 선택 사항)", "模型是否带音高指导,1是0否": "모델에 음높이 안내가 있는지 여부, 1이면 있음 0이면 없음", "模型版本型号": "모델 버전 및 모델 번호", "模型融合, 可用于测试音色融合": "모델 통합, 음색 통합 테스트에 사용 가능", "模型路径": "모델 경로", "淡入淡出长度": "페이드 인/아웃 길이", "版本": "버전", "特征提取": "특성 추출", "特征检索库文件路径,为空则使用下拉的选择结果": "특성 검색 라이브러리 파일 경로, 비어 있으면 드롭다운 선택 결과 사용", "男转女推荐+12key, 女转男推荐-12key, 如果音域爆炸导致音色失真也可以自己调整到合适音域. ": "남성을 여성으로 추천 +12키, 여성을 남성으로 추천 -12키, 음역 폭발로 음색이 왜곡되면 적절한 음역으로 직접 조절 가능", "目标采样率": "목표 샘플링률", "算法延迟(ms):": "알고리즘 지연 시간(ms):", "自动检测index路径,下拉式选择(dropdown)": "자동으로 index 경로 감지, 드롭다운 선택", "融合": "융합", "要改的模型信息": "수정할 모델 정보", "要置入的模型信息": "삽입할 모델 정보", "训练": "훈련", "训练模型": "모델 훈련", "训练特征索引": "특성 인덱스 훈련", "训练结束, 您可查看控制台训练日志或实验文件夹下的train.log": "훈련 종료, 콘솔 훈련 로그 또는 실험 폴더의 train.log를 확인할 수 있습니다", "请指定说话人id": "화자 ID 지정", "请选择index文件": "index 파일 선택", "请选择pth文件": "pth 파일 선택", "请选择说话人id": "화자 ID 선택", "转换": "변환", "输入实验名": "실험명 입력", "输入待处理音频文件夹路径": "처리 대기 중인 오디오 폴더 경로 입력", "输入待处理音频文件夹路径(去文件管理器地址栏拷就行了)": "처리 대기 중인 오디오 폴더 경로 입력(파일 관리자 주소 표시 줄에서 복사하면 됨)", "输入待处理音频文件路径(默认是正确格式示例)": "처리 대기 중인 오디오 파일 경로 입력(기본적으로 올바른 형식의 예제)", "输入源音量包络替换输出音量包络融合比例，越靠近1越使用输出包络": "소스 음량 에너벌롭을 입력하여 출력 음량 에너벌롭 합성 비율을 대체하면 1에 가까울수록 출력 에너벌롭 사용", "输入监听": "입력 모니터링", "输入训练文件夹路径": "훈련 폴더 경로 입력", "输入设备": "입력 장치", "输入降噪": "노이즈 감소 입력", "输出信息": "출력 정보", "输出变声": "음성 출력", "输出设备": "출력 장치", "输出降噪": "노이즈 감소 출력", "输出音频(右下角三个点,点了可以下载)": "출력 오디오(우하단 세 점, 클릭하면 다운로드 가능)", "选择.index文件": "index 파일 선택", "选择.pth文件": "pth 파일 선택", "选择音高提取算法,输入歌声可用pm提速,harvest低音好但巨慢无比,crepe效果好但吃GPU": "음높이 추출 알고리즘 선택, 노래 입력에 pm 사용 가능, harvest는 저음이 좋지만 매우 느림, crepe 효과는 좋지만 GPU 사용", "选择音高提取算法,输入歌声可用pm提速,harvest低音好但巨慢无比,crepe效果好但吃GPU,rmvpe效果最好且微吃GPU": "음높이 추출 알고리즘 선택, 노래 입력에 pm 사용 가능, harvest는 저음이 좋지만 매우 느림, crepe 효과는 좋지만 GPU 사용, rmvpe 효과가 가장 좋으며 약간의 GPU 사용", "选择音高提取算法:输入歌声可用pm提速,高质量语音但CPU差可用dio提速,harvest质量更好但慢,rmvpe效果最好且微吃CPU/GPU": "음높이 추출 알고리즘 선택: 노래 입력에 pm 사용 가능, 고품질 음성이지만 CPU가 낮음, dio 사용 가능, harvest 품질이 더 좋지만 느림, rmvpe 효과가 최고이며 CPU/GPU 약간 사용", "采样率:": "샘플링률:", "采样长度": "샘플링 길이", "重载设备列表": "장치 목록 다시로드", "音调设置": "음조 설정", "音频设备(请使用同种类驱动)": "오디오 장치(동일한 유형의 드라이버 사용 권장)", "音高算法": "음높이 알고리즘", "额外推理时长": "추가 추론 시간"}