{"很遗憾您这没有能用的显卡来支持您训练": "很遗憾您这没有能用的显卡来支持您训练", "UVR5已开启": "UVR5已开启", "UVR5已关闭": "UVR5已关闭", "输入文件夹路径": "输入文件夹路径", "输出文件夹路径": "输出文件夹路径", "ASR 模型": "ASR 模型", "ASR 模型尺寸": "ASR 模型尺寸", "ASR 语言设置": "ASR 语言设置", "模型切换": "模型切换", "是否开启dpo训练选项(实验性)": "是否开启dpo训练选项(实验性)", "开启无参考文本模式。不填参考文本亦相当于开启。": "开启无参考文本模式。不填参考文本亦相当于开启。", "使用无参考文本模式时建议使用微调的GPT": "使用无参考文本模式时建议使用微调的GPT", "后续将支持转音素、手工修改音素、语音合成分步执行。": "后续将支持转音素、手工修改音素、语音合成分步执行。", "gpt采样参数(无参考文本时不要太低)：": "gpt采样参数(无参考文本时不要太低)：", "按标点符号切": "按标点符号切", "本软件以MIT协议开源, 作者不对软件具备任何控制力, 使用软件者、传播软件导出的声音者自负全责. <br>如不认可该条款, 则不能使用或引用软件包内任何代码和文件. 详见根目录<b>LICENSE</b>.": "本软件以MIT协议开源, 作者不对软件具备任何控制力, 使用软件者、传播软件导出的声音者自负全责. <br>如不认可该条款, 则不能使用或引用软件包内任何代码和文件. 详见根目录<b>LICENSE</b>.", "0-前置数据集获取工具": "0-前置数据集获取工具", "0a-UVR5人声伴奏分离&去混响去延迟工具": "0a-UVR5人声伴奏分离&去混响去延迟工具", "是否开启UVR5-WebUI": "是否开启UVR5-WebUI", "UVR5进程输出信息": "UVR5进程输出信息", "0b-语音切分工具": "0b-语音切分工具", ".list标注文件的路径": ".list标注文件的路径", "GPT模型列表": "GPT模型列表", "SoVITS模型列表": "SoVITS模型列表", "填切割后音频所在目录！读取的音频文件完整路径=该目录-拼接-list文件里波形对应的文件名（不是全路径）。": "填切割后音频所在目录！读取的音频文件完整路径=该目录-拼接-list文件里波形对应的文件名（不是全路径）。", "音频自动切分输入路径，可文件可文件夹": "音频自动切分输入路径，可文件可文件夹", "切分后的子音频的输出根目录": "切分后的子音频的输出根目录", "怎么切": "怎么切", "不切": "不切", "凑四句一切": "凑四句一切", "按英文句号.切": "按英文句号.切", "threshold:音量小于这个值视作静音的备选切割点": "threshold:音量小于这个值视作静音的备选切割点", "min_length:每段最小多长，如果第一段太短一直和后面段连起来直到超过这个值": "min_length:每段最小多长，如果第一段太短一直和后面段连起来直到超过这个值", "min_interval:最短切割间隔": "min_interval:最短切割间隔", "hop_size:怎么算音量曲线，越小精度越大计算量越高（不是精度越大效果越好）": "hop_size:怎么算音量曲线，越小精度越大计算量越高（不是精度越大效果越好）", "max_sil_kept:切完后静音最多留多长": "max_sil_kept:切完后静音最多留多长", "开启语音切割": "开启语音切割", "终止语音切割": "终止语音切割", "max:归一化后最大值多少": "max:归一化后最大值多少", "alpha_mix:混多少比例归一化后音频进来": "alpha_mix:混多少比例归一化后音频进来", "切割使用的进程数": "切割使用的进程数", "语音切割进程输出信息": "语音切割进程输出信息", "0c-中文批量离线ASR工具": "0c-中文批量离线ASR工具", "开启离线批量ASR": "开启离线批量ASR", "终止ASR进程": "终止ASR进程", "批量ASR(中文only)输入文件夹路径": "批量ASR(中文only)输入文件夹路径", "ASR进程输出信息": "ASR进程输出信息", "0d-语音文本校对标注工具": "0d-语音文本校对标注工具", "是否开启打标WebUI": "是否开启打标WebUI", "打标数据标注文件路径": "打标数据标注文件路径", "打标工具进程输出信息": "打标工具进程输出信息", "1-GPT-SoVITS-TTS": "1-GPT-SoVITS-TTS", "*实验/模型名": "*实验/模型名", "显卡信息": "显卡信息", "预训练的SoVITS-G模型路径": "预训练的SoVITS-G模型路径", "预训练的SoVITS-D模型路径": "预训练的SoVITS-D模型路径", "预训练的GPT模型路径": "预训练的GPT模型路径", "1A-训练集格式化工具": "1A-训练集格式化工具", "输出logs/实验名目录下应有23456开头的文件和文件夹": "输出logs/实验名目录下应有23456开头的文件和文件夹", "*文本标注文件": "*文本标注文件", "*训练集音频文件目录": "*训练集音频文件目录", "训练集音频文件目录 拼接 list文件里波形对应的文件名。": "训练集音频文件目录 拼接 list文件里波形对应的文件名。", "1Aa-文本内容": "1Aa-文本内容", "GPU卡号以-分割，每个卡号一个进程": "GPU卡号以-分割，每个卡号一个进程", "预训练的中文BERT模型路径": "预训练的中文BERT模型路径", "开启文本获取": "开启文本获取", "终止文本获取进程": "终止文本获取进程", "文本进程输出信息": "文本进程输出信息", "1Ab-SSL自监督特征提取": "1Ab-SSL自监督特征提取", "预训练的SSL模型路径": "预训练的SSL模型路径", "开启SSL提取": "开启SSL提取", "终止SSL提取进程": "终止SSL提取进程", "SSL进程输出信息": "SSL进程输出信息", "1Ac-语义token提取": "1Ac-语义token提取", "开启语义token提取": "开启语义token提取", "终止语义token提取进程": "终止语义token提取进程", "语义token提取进程输出信息": "语义token提取进程输出信息", "1Aabc-训练集格式化一键三连": "1Aabc-训练集格式化一键三连", "开启一键三连": "开启一键三连", "终止一键三连": "终止一键三连", "一键三连进程输出信息": "一键三连进程输出信息", "1B-微调训练": "1B-微调训练", "1Ba-SoVITS训练。用于分享的模型文件输出在SoVITS_weights下。": "1Ba-SoVITS训练。用于分享的模型文件输出在SoVITS_weights下。", "每张显卡的batch_size": "每张显卡的batch_size", "总训练轮数total_epoch，不建议太高": "总训练轮数total_epoch，不建议太高", "文本模块学习率权重": "文本模块学习率权重", "保存频率save_every_epoch": "保存频率save_every_epoch", "是否仅保存最新的ckpt文件以节省硬盘空间": "是否仅保存最新的ckpt文件以节省硬盘空间", "是否在每次保存时间点将最终小模型保存至weights文件夹": "是否在每次保存时间点将最终小模型保存至weights文件夹", "开启SoVITS训练": "开启SoVITS训练", "终止SoVITS训练": "终止SoVITS训练", "SoVITS训练进程输出信息": "SoVITS训练进程输出信息", "1Bb-GPT训练。用于分享的模型文件输出在GPT_weights下。": "1Bb-GPT训练。用于分享的模型文件输出在GPT_weights下。", "总训练轮数total_epoch": "总训练轮数total_epoch", "开启GPT训练": "开启GPT训练", "终止GPT训练": "终止GPT训练", "GPT训练进程输出信息": "GPT训练进程输出信息", "1C-推理": "1C-推理", "选择训练完存放在SoVITS_weights和GPT_weights下的模型。默认的一个是底模，体验5秒Zero Shot TTS用。": "选择训练完存放在SoVITS_weights和GPT_weights下的模型。默认的一个是底模，体验5秒Zero Shot TTS用。", "*GPT模型列表": "*GPT模型列表", "*SoVITS模型列表": "*SoVITS模型列表", "GPU卡号,只能填1个整数": "GPU卡号,只能填1个整数", "刷新模型路径": "刷新模型路径", "是否开启TTS推理WebUI": "是否开启TTS推理WebUI", "TTS推理WebUI进程输出信息": "TTS推理WebUI进程输出信息", "2-GPT-SoVITS-变声": "2-GPT-SoVITS-变声", "施工中，请静候佳音": "施工中，请静候佳音", "参考音频在3~10秒范围外，请更换！": "参考音频在3~10秒范围外，请更换！", "请上传3~10秒内参考音频，超过会报错！": "请上传3~10秒内参考音频，超过会报错！", "TTS推理进程已开启": "TTS推理进程已开启", "TTS推理进程已关闭": "TTS推理进程已关闭", "打标工具WebUI已开启": "打标工具WebUI已开启", "打标工具WebUI已关闭": "打标工具WebUI已关闭", "本软件以MIT协议开源, 作者不对软件具备任何控制力, 使用软件者、传播软件导出的声音者自负全责. 如不认可该条款, 则不能使用或引用软件包内任何代码和文件. 详见根目录LICENSE.": "本软件以MIT协议开源, 作者不对软件具备任何控制力, 使用软件者、传播软件导出的声音者自负全责. 如不认可该条款, 则不能使用或引用软件包内任何代码和文件. 详见根目录LICENSE.", "*请上传并填写参考信息": "*请上传并填写参考信息", "*请填写需要合成的目标文本。中英混合选中文，日英混合选日文，中日混合暂不支持，非目标语言文本自动遗弃。": "*请填写需要合成的目标文本。中英混合选中文，日英混合选日文，中日混合暂不支持，非目标语言文本自动遗弃。", "ASR任务开启：%s": "ASR任务开启：%s", "GPT训练完成": "GPT训练完成", "GPT训练开始：%s": "GPT训练开始：%s", "SSL提取进程执行中": "SSL提取进程执行中", "SSL提取进程结束": "SSL提取进程结束", "SoVITS训练完成": "SoVITS训练完成", "SoVITS训练开始：%s": "SoVITS训练开始：%s", "一键三连中途报错": "一键三连中途报错", "一键三连进程结束": "一键三连进程结束", "中文": "中文", "凑50字一切": "凑50字一切", "凑五句一切": "凑五句一切", "切分后文本": "切分后文本", "切割执行中": "切割执行中", "切割结束": "切割结束", "参考音频的文本": "参考音频的文本", "参考音频的语种": "参考音频的语种", "合成语音": "合成语音", "后续将支持混合语种编码文本输入。": "后续将支持混合语种编码文本输入。", "已有正在进行的ASR任务，需先终止才能开启下一次任务": "已有正在进行的ASR任务，需先终止才能开启下一次任务", "已有正在进行的GPT训练任务，需先终止才能开启下一次任务": "已有正在进行的GPT训练任务，需先终止才能开启下一次任务", "已有正在进行的SSL提取任务，需先终止才能开启下一次任务": "已有正在进行的SSL提取任务，需先终止才能开启下一次任务", "已有正在进行的SoVITS训练任务，需先终止才能开启下一次任务": "已有正在进行的SoVITS训练任务，需先终止才能开启下一次任务", "已有正在进行的一键三连任务，需先终止才能开启下一次任务": "已有正在进行的一键三连任务，需先终止才能开启下一次任务", "已有正在进行的切割任务，需先终止才能开启下一次任务": "已有正在进行的切割任务，需先终止才能开启下一次任务", "已有正在进行的文本任务，需先终止才能开启下一次任务": "已有正在进行的文本任务，需先终止才能开启下一次任务", "已有正在进行的语义token提取任务，需先终止才能开启下一次任务": "已有正在进行的语义token提取任务，需先终止才能开启下一次任务", "已终止ASR进程": "已终止ASR进程", "已终止GPT训练": "已终止GPT训练", "已终止SoVITS训练": "已终止SoVITS训练", "已终止所有1a进程": "已终止所有1a进程", "已终止所有1b进程": "已终止所有1b进程", "已终止所有一键三连进程": "已终止所有一键三连进程", "已终止所有切割进程": "已终止所有切割进程", "已终止所有语义token进程": "已终止所有语义token进程", "按中文句号。切": "按中文句号。切", "文本切分工具。太长的文本合成出来效果不一定好，所以太长建议先切。合成会根据文本的换行分开合成再拼起来。": "文本切分工具。太长的文本合成出来效果不一定好，所以太长建议先切。合成会根据文本的换行分开合成再拼起来。", "文本进程执行中": "文本进程执行中", "文本进程结束": "文本进程结束", "日文": "日文", "英文": "英文", "语义token提取进程执行中": "语义token提取进程执行中", "语义token提取进程结束": "语义token提取进程结束", "请上传参考音频": "请上传参考音频", "输入路径不存在": "输入路径不存在", "输入路径存在但既不是文件也不是文件夹": "输入路径存在但既不是文件也不是文件夹", "输出的语音": "输出的语音", "进度：1a-done": "进度：1a-done", "进度：1a-done, 1b-ing": "进度：1a-done, 1b-ing", "进度：1a-ing": "进度：1a-ing", "进度：1a1b-done": "进度：1a1b-done", "进度：1a1b-done, 1cing": "进度：1a1b-done, 1cing", "进度：all-done": "进度：all-done", "需要合成的切分前文本": "需要合成的切分前文本", "需要合成的文本": "需要合成的文本", "需要合成的语种": "需要合成的语种", ">=3则使用对harvest音高识别的结果使用中值滤波，数值为滤波半径，使用可以削弱哑音": ">=3则使用对harvest音高识别的结果使用中值滤波，数值为滤波半径，使用可以削弱哑音", "A模型权重": "A模型权重", "A模型路径": "A模型路径", "B模型路径": "B模型路径", "E:\\语音音频+标注\\米津玄师\\src": "E:\\语音音频+标注\\米津玄师\\src", "F0曲线文件, 可选, 一行一个音高, 代替默认F0及升降调": "F0曲线文件, 可选, 一行一个音高, 代替默认F0及升降调", "Index Rate": "Index Rate", "Onnx导出": "Onnx导出", "Onnx输出路径": "Onnx输出路径", "RVC模型路径": "RVC模型路径", "ckpt处理": "ckpt处理", "harvest进程数": "harvest进程数", "index文件路径不可包含中文": "index文件路径不可包含中文", "pth文件路径不可包含中文": "pth文件路径不可包含中文", "rmvpe卡号配置：以-分隔输入使用的不同进程卡号,例如0-0-1使用在卡0上跑2个进程并在卡1上跑1个进程": "rmvpe卡号配置：以-分隔输入使用的不同进程卡号,例如0-0-1使用在卡0上跑2个进程并在卡1上跑1个进程", "step1: 填写实验配置. 实验数据放在logs下, 每个实验一个文件夹, 需手工输入实验名路径, 内含实验配置, 日志, 训练得到的模型文件. ": "step1: 填写实验配置. 实验数据放在logs下, 每个实验一个文件夹, 需手工输入实验名路径, 内含实验配置, 日志, 训练得到的模型文件. ", "step1:正在处理数据": "step1:正在处理数据", "step2:正在提取音高&正在提取特征": "step2:正在提取音高&正在提取特征", "step2a: 自动遍历训练文件夹下所有可解码成音频的文件并进行切片归一化, 在实验目录下生成2个wav文件夹; 暂时只支持单人训练. ": "step2a: 自动遍历训练文件夹下所有可解码成音频的文件并进行切片归一化, 在实验目录下生成2个wav文件夹; 暂时只支持单人训练. ", "step2b: 使用CPU提取音高(如果模型带音高), 使用GPU提取特征(选择卡号)": "step2b: 使用CPU提取音高(如果模型带音高), 使用GPU提取特征(选择卡号)", "step3: 填写训练设置, 开始训练模型和索引": "step3: 填写训练设置, 开始训练模型和索引", "step3a:正在训练模型": "step3a:正在训练模型", "一键训练": "一键训练", "也可批量输入音频文件, 二选一, 优先读文件夹": "也可批量输入音频文件, 二选一, 优先读文件夹", "人声伴奏分离批量处理， 使用UVR5模型。 <br>合格的文件夹路径格式举例： E:\\codes\\py39\\vits_vc_gpu\\白鹭霜华测试样例(去文件管理器地址栏拷就行了)。 <br>模型分为三类： <br>1、保留人声：不带和声的音频选这个，对主人声保留比HP5更好。内置HP2和HP3两个模型，HP3可能轻微漏伴奏但对主人声保留比HP2稍微好一丁点； <br>2、仅保留主人声：带和声的音频选这个，对主人声可能有削弱。内置HP5一个模型； <br> 3、去混响、去延迟模型（by FoxJoy）：<br>  (1)MDX-Net(onnx_dereverb):对于双通道混响是最好的选择，不能去除单通道混响；<br>&emsp;(234)DeEcho:去除延迟效果。Aggressive比Normal去除得更彻底，DeReverb额外去除混响，可去除单声道混响，但是对高频重的板式混响去不干净。<br>去混响/去延迟，附：<br>1、DeEcho-DeReverb模型的耗时是另外2个DeEcho模型的接近2倍；<br>2、MDX-Net-Dereverb模型挺慢的；<br>3、个人推荐的最干净的配置是先MDX-Net再DeEcho-Aggressive。": "人声伴奏分离批量处理， 使用UVR5模型。 <br>合格的文件夹路径格式举例： E:\\codes\\py39\\vits_vc_gpu\\白鹭霜华测试样例(去文件管理器地址栏拷就行了)。 <br>模型分为三类： <br>1、保留人声：不带和声的音频选这个，对主人声保留比HP5更好。内置HP2和HP3两个模型，HP3可能轻微漏伴奏但对主人声保留比HP2稍微好一丁点； <br>2、仅保留主人声：带和声的音频选这个，对主人声可能有削弱。内置HP5一个模型； <br> 3、去混响、去延迟模型（by FoxJoy）：<br>  (1)MDX-Net(onnx_dereverb):对于双通道混响是最好的选择，不能去除单通道混响；<br>&emsp;(234)DeEcho:去除延迟效果。Aggressive比Normal去除得更彻底，DeReverb额外去除混响，可去除单声道混响，但是对高频重的板式混响去不干净。<br>去混响/去延迟，附：<br>1、DeEcho-DeReverb模型的耗时是另外2个DeEcho模型的接近2倍；<br>2、MDX-Net-Dereverb模型挺慢的；<br>3、个人推荐的最干净的配置是先MDX-Net再DeEcho-Aggressive。", "以-分隔输入使用的卡号, 例如   0-1-2   使用卡0和卡1和卡2": "以-分隔输入使用的卡号, 例如   0-1-2   使用卡0和卡1和卡2", "伴奏人声分离&去混响&去回声": "伴奏人声分离&去混响&去回声", "使用模型采样率": "使用模型采样率", "使用设备采样率": "使用设备采样率", "保存名": "保存名", "保存的文件名, 默认空为和源文件同名": "保存的文件名, 默认空为和源文件同名", "保存的模型名不带后缀": "保存的模型名不带后缀", "保护清辅音和呼吸声，防止电音撕裂等artifact，拉满0.5不开启，调低加大保护力度但可能降低索引效果": "保护清辅音和呼吸声，防止电音撕裂等artifact，拉满0.5不开启，调低加大保护力度但可能降低索引效果", "修改": "修改", "修改模型信息(仅支持weights文件夹下提取的小模型文件)": "修改模型信息(仅支持weights文件夹下提取的小模型文件)", "停止音频转换": "停止音频转换", "全流程结束！": "全流程结束！", "刷新音色列表和索引路径": "刷新音色列表和索引路径", "加载模型": "加载模型", "加载预训练底模D路径": "加载预训练底模D路径", "加载预训练底模G路径": "加载预训练底模G路径", "单次推理": "单次推理", "卸载音色省显存": "卸载音色省显存", "变调(整数, 半音数量, 升八度12降八度-12)": "变调(整数, 半音数量, 升八度12降八度-12)", "后处理重采样至最终采样率，0为不进行重采样": "后处理重采样至最终采样率，0为不进行重采样", "否": "否", "启用相位声码器": "启用相位声码器", "响应阈值": "响应阈值", "响度因子": "响度因子", "处理数据": "处理数据", "导出Onnx模型": "导出Onnx模型", "导出文件格式": "导出文件格式", "常见问题解答": "常见问题解答", "常规设置": "常规设置", "开始音频转换": "开始音频转换", "性能设置": "性能设置", "批量推理": "批量推理", "批量转换, 输入待转换音频文件夹, 或上传多个音频文件, 在指定文件夹(默认opt)下输出转换的音频. ": "批量转换, 输入待转换音频文件夹, 或上传多个音频文件, 在指定文件夹(默认opt)下输出转换的音频. ", "指定输出主人声文件夹": "指定输出主人声文件夹", "指定输出文件夹": "指定输出文件夹", "指定输出非主人声文件夹": "指定输出非主人声文件夹", "推理时间(ms):": "推理时间(ms):", "推理音色": "推理音色", "提取": "提取", "提取音高和处理数据使用的CPU进程数": "提取音高和处理数据使用的CPU进程数", "是": "是", "是否缓存所有训练集至显存. 10min以下小数据可缓存以加速训练, 大数据缓存会炸显存也加不了多少速": "是否缓存所有训练集至显存. 10min以下小数据可缓存以加速训练, 大数据缓存会炸显存也加不了多少速", "查看": "查看", "查看模型信息(仅支持weights文件夹下提取的小模型文件)": "查看模型信息(仅支持weights文件夹下提取的小模型文件)", "检索特征占比": "检索特征占比", "模型": "模型", "模型推理": "模型推理", "模型提取(输入logs文件夹下大文件模型路径),适用于训一半不想训了模型没有自动提取保存小文件模型,或者想测试中间模型的情况": "模型提取(输入logs文件夹下大文件模型路径),适用于训一半不想训了模型没有自动提取保存小文件模型,或者想测试中间模型的情况", "模型是否带音高指导": "模型是否带音高指导", "模型是否带音高指导(唱歌一定要, 语音可以不要)": "模型是否带音高指导(唱歌一定要, 语音可以不要)", "模型是否带音高指导,1是0否": "模型是否带音高指导,1是0否", "模型版本型号": "模型版本型号", "模型融合, 可用于测试音色融合": "模型融合, 可用于测试音色融合", "模型路径": "模型路径", "淡入淡出长度": "淡入淡出长度", "版本": "版本", "特征提取": "特征提取", "特征检索库文件路径,为空则使用下拉的选择结果": "特征检索库文件路径,为空则使用下拉的选择结果", "男转女推荐+12key, 女转男推荐-12key, 如果音域爆炸导致音色失真也可以自己调整到合适音域. ": "男转女推荐+12key, 女转男推荐-12key, 如果音域爆炸导致音色失真也可以自己调整到合适音域. ", "目标采样率": "目标采样率", "算法延迟(ms):": "算法延迟(ms):", "自动检测index路径,下拉式选择(dropdown)": "自动检测index路径,下拉式选择(dropdown)", "融合": "融合", "要改的模型信息": "要改的模型信息", "要置入的模型信息": "要置入的模型信息", "训练": "训练", "训练模型": "训练模型", "训练特征索引": "训练特征索引", "训练结束, 您可查看控制台训练日志或实验文件夹下的train.log": "训练结束, 您可查看控制台训练日志或实验文件夹下的train.log", "请指定说话人id": "请指定说话人id", "请选择index文件": "请选择index文件", "请选择pth文件": "请选择pth文件", "请选择说话人id": "请选择说话人id", "转换": "转换", "输入实验名": "输入实验名", "输入待处理音频文件夹路径": "输入待处理音频文件夹路径", "输入待处理音频文件夹路径(去文件管理器地址栏拷就行了)": "输入待处理音频文件夹路径(去文件管理器地址栏拷就行了)", "输入待处理音频文件路径(默认是正确格式示例)": "输入待处理音频文件路径(默认是正确格式示例)", "输入源音量包络替换输出音量包络融合比例，越靠近1越使用输出包络": "输入源音量包络替换输出音量包络融合比例，越靠近1越使用输出包络", "输入监听": "输入监听", "输入训练文件夹路径": "输入训练文件夹路径", "输入设备": "输入设备", "输入降噪": "输入降噪", "输出信息": "输出信息", "输出变声": "输出变声", "输出设备": "输出设备", "输出降噪": "输出降噪", "输出音频(右下角三个点,点了可以下载)": "输出音频(右下角三个点,点了可以下载)", "选择.index文件": "选择.index文件", "选择.pth文件": "选择.pth文件", "选择音高提取算法,输入歌声可用pm提速,harvest低音好但巨慢无比,crepe效果好但吃GPU": "选择音高提取算法,输入歌声可用pm提速,harvest低音好但巨慢无比,crepe效果好但吃GPU", "选择音高提取算法,输入歌声可用pm提速,harvest低音好但巨慢无比,crepe效果好但吃GPU,rmvpe效果最好且微吃GPU": "选择音高提取算法,输入歌声可用pm提速,harvest低音好但巨慢无比,crepe效果好但吃GPU,rmvpe效果最好且微吃GPU", "选择音高提取算法:输入歌声可用pm提速,高质量语音但CPU差可用dio提速,harvest质量更好但慢,rmvpe效果最好且微吃CPU/GPU": "选择音高提取算法:输入歌声可用pm提速,高质量语音但CPU差可用dio提速,harvest质量更好但慢,rmvpe效果最好且微吃CPU/GPU", "采样率:": "采样率:", "采样长度": "采样长度", "重载设备列表": "重载设备列表", "音调设置": "音调设置", "音频设备(请使用同种类驱动)": "音频设备(请使用同种类驱动)", "音高算法": "音高算法", "额外推理时长": "额外推理时长"}