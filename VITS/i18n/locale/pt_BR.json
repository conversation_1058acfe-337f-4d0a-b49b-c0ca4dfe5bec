{"很遗憾您这没有能用的显卡来支持您训练": "Infelizmente, você não possui uma placa de vídeo funcional para suportar seu treinamento", "UVR5已开启": "UVR5 está ativado", "UVR5已关闭": "UVR5 está desativado", "本软件以MIT协议开源, 作者不对软件具备任何控制力, 使用软件者、传播软件导出的声音者自负全责. <br>如不认可该条款, 则不能使用或引用软件包内任何代码和文件. 详见根目录<b>LICENSE</b>.": "Este software é de código aberto sob a licença MIT. O autor não tem controle sobre o software. Aqueles que usam o software e difundem os sons exportados pelo software são totalmente responsáveis. <br>Se você não concorda com esta cláusula, não pode usar ou citar nenhum código e arquivo dentro do pacote de software. Consulte o diretório raiz <b>LICENSE</b> para mais detalhes.<br><br> Traduzido por <PERSON>", "0-前置数据集获取工具": "0- Ferramenta de aquisição de conjunto de dados pré-frontal", "0a-UVR5人声伴奏分离&去混响去延迟工具": "0A-UVR5 separação de voz e acompanhamento instrumental & ferramenta para remover reverberação e atraso", "是否开启UVR5-WebUI": "Se deseja ativar a UVR5-WEBUI", "UVR5进程输出信息": "Informações de saída do processo UVR5", "0b-语音切分工具": "0b- Ferramenta de corte de voz", "音频自动切分输入路径，可文件可文件夹": "Caminho de entrada automático de corte de áudio, pode ser um arquivo ou uma pasta", "切分后的子音频的输出根目录": "Diretório raiz de saída do sub-áudio após o corte", "threshold:音量小于这个值视作静音的备选切割点": "Limiar: O volume menor que este valor é considerado como um ponto de corte mudo alternativo", "min_length:每段最小多长，如果第一段太短一直和后面段连起来直到超过这个值": "min_length: O comprimento mínimo de cada parágrafo, se o primeiro for muito curto, conecte-o continuamente aos próximos até ultrapassar este valor", "min_interval:最短切割间隔": "min_interval: O intervalo de corte mínimo", "hop_size:怎么算音量曲线，越小精度越大计算量越高（不是精度越大效果越好）": "HOP_SIZE: Como calcular a curva de volume, quanto menor a precisão, maior a quantidade de cálculos (não significa que quanto maior a precisão, melhor o efeito)", "max_sil_kept:切完后静音最多留多长": "max_sil_kept: <PERSON><PERSON><PERSON> de cortar, por quanto tempo no máximo o silêncio é mantido", "开启语音切割": "Ativar corte de voz", "终止语音切割": "Encerrar corte de voz", "max:归一化后最大值多少": "MAX: Qual é o valor máximo após a normalização?", "alpha_mix:混多少比例归一化后音频进来": "alpha_mix: Em que proporção o áudio normalizado é misturado de volta", "切割使用的进程数": "Número de processos para corte", "语音切割进程输出信息": "Informações de saída do processo de corte de voz", "0c-中文批量离线ASR工具": "0c- Ferramenta chinesa de ASR offline em lote", "开启离线批量ASR": "Ativar ASR offline em lote", "终止ASR进程": "Encerrar processo ASR", "批量ASR(中文only)输入文件夹路径": "<PERSON>in<PERSON> da pasta de entrada para ASR em lote (apenas chinês)", "ASR进程输出信息": "Informações de saída do processo ASR", "0d-语音文本校对标注工具": "0d- Ferramenta de correção e marcação de texto de voz", "是否开启打标WebUI": "Se deseja abrir o webui de marcação", "打标数据标注文件路径": "Caminho do arquivo de marcação de dados de marcação", "打标工具进程输出信息": "Informações de saída do processo da ferramenta de marcação", "1-GPT-SoVITS-TTS": "1-GPT-SOVITS-TTS", "*实验/模型名": "*Nome do experimento/modelo", "显卡信息": "Informações da placa de vídeo", "预训练的SoVITS-G模型路径": "Caminho do modelo SoVITS-G pre-train", "预训练的SoVITS-D模型路径": "Caminho do modelo SoVITS-D pre-train", "预训练的GPT模型路径": "Caminho do modelo GPT pre-train", "1A-训练集格式化工具": "1A-Ferramenta de formatação de conjunto de dados de treinamento", "输出logs/实验名目录下应有23456开头的文件和文件夹": "Logs de saída/deve haver arquivos e pastas começando com 23456 no diretório do nome do experimento", "*文本标注文件": "*Arquivo de marcação de texto", "*训练集音频文件目录": "*Diretório de arquivos de áudio do conjunto de treinamento", "训练集音频文件目录 拼接 list文件里波形对应的文件名。": "Diretório de arquivos de áudio do conjunto de treinamento. Concatene o nome do arquivo correspondente à forma de onda no arquivo de lista", "1Aa-文本内容": "1AA-Conteúdo do texto", "GPU卡号以-分割，每个卡号一个进程": "Número da placa de vídeo dividido por-, cada número de placa é um processo", "预训练的中文BERT模型路径": "Caminho do modelo BERT chinês pre-train", "开启文本获取": "Ativar obtenção de texto", "终止文本获取进程": "Encerrar processo de obtenção de texto", "文本进程输出信息": "Informações de saída do processo de texto", "1Ab-SSL自监督特征提取": "1AB-Extração de características auto-supervisionadas SSL", "预训练的SSL模型路径": "Caminho do modelo SSL pre-train", "开启SSL提取": "Ativar extração SSL", "终止SSL提取进程": "Encerrar processo de extração SSL", "SSL进程输出信息": "Informações de saída do processo SSL", "1Ac-语义token提取": "1AC-Extração de token semântico", "开启语义token提取": "Ativar extração de token semântico", "终止语义token提取进程": "Encerrar processo de extração de token semântico", "语义token提取进程输出信息": "Informações de saída do processo de extração de token semântico", "1Aabc-训练集格式化一键三连": "1AABC-Formatação de conjunto de treinamento em um clique", "开启一键三连": "Ativar um clique", "终止一键三连": "Encerrar um clique", "一键三连进程输出信息": "Informações de saída do processo de um clique", "1B-微调训练": "1B-Treinamento de ajuste fino", "1Ba-SoVITS训练。用于分享的模型文件输出在SoVITS_weights下。": "1ba-Treinamento SoVITS. O arquivo de modelo para compartilhamento é gerado em SOVITS_WEIGHTS", "每张显卡的batch_size": "Tamanho do lote de cada placa de vídeo", "总训练轮数total_epoch，不建议太高": "Total de epoch de treinamento, não é recomendável um valor muito alto", "文本模块学习率权重": "Weight da taxa de aprendizado do módulo de texto", "保存频率save_every_epoch": "Frequência de salvamento save_every_epoch", "是否仅保存最新的ckpt文件以节省硬盘空间": "Se deve salvar apenas o último arquivo CKPT para economizar espaço em disco", "是否在每次保存时间点将最终小模型保存至weights文件夹": "Se deve salvar o modelo pequeno final na pasta Weights em cada ponto de salvamento de tempo", "开启SoVITS训练": "Ativar treinamento SoVITS", "终止SoVITS训练": "Encerrar treinamento SoVITS", "SoVITS训练进程输出信息": "Informações de saída do processo de treinamento SoVITS", "1Bb-GPT训练。用于分享的模型文件输出在GPT_weights下。": "1BB-Treinamento GPT. O arquivo de modelo para compartilhamento é gerado em GPT_WEIGHTS", "总训练轮数total_epoch": "Total de epoch de treinamento", "开启GPT训练": "Ativar treinamento GPT", "终止GPT训练": "Encerrar treinamento GPT", "GPT训练进程输出信息": "Informações de saída do processo de treinamento GPT", "1C-推理": "1C-raciocínio", "选择训练完存放在SoVITS_weights和GPT_weights下的模型。默认的一个是底模，体验5秒Zero Shot TTS用。": "Selecione os modelos armazenados em Sovits_weights e GPT_WEIGHTS. O padrão é o modelo inferior, experiência para 5 segundos de Zero Shot TTS", "*GPT模型列表": "*Lista de modelos GPT", "*SoVITS模型列表": "*Lista de modelos Sovits", "GPU卡号,只能填1个整数": "Número da placa de vídeo, só é possível preencher com um número inteiro", "刷新模型路径": "Atualizar caminho do modelo", "是否开启TTS推理WebUI": "Se deseja ativar o webui de raciocínio TTS", "TTS推理WebUI进程输出信息": "Informações de saída do processo webui de raciocínio TTS", "2-GPT-SoVITS-变声": "2-gpt-sovits-mudança de voz", "施工中，请静候佳音": "Em construção, por favor, aguarde por um bom som", "TTS推理进程已开启": "O processo de inferência TTS foi iniciado", "TTS推理进程已关闭": "O processo de inferência TTS foi desativado", "打标工具WebUI已开启": "A ferramenta de marcação WebUI está ativada", "打标工具WebUI已关闭": "A ferramenta de marcação WebUI foi desativado"}