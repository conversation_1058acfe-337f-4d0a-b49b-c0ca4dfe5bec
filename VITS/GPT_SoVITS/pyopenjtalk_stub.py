"""
pyopenjtalk的临时替代模块
用于绕过日语处理依赖问题
"""

def g2p(text, kana=False, join=True):
    """
    简化的日语文本到音素转换
    """
    if kana:
        # 返回假名
        return text
    else:
        # 返回音素（简化处理）
        if join:
            return " ".join(list(text))
        else:
            return list(text)

def run_frontend(text):
    """
    简化的前端处理
    """
    return [{"phoneme": char, "mora": char} for char in text]

# 为了兼容性，添加其他可能用到的函数
def extract_fullcontext(text):
    """提取全上下文标签"""
    return [f"xx-{char}+xx" for char in text]

def make_label(text):
    """生成标签"""
    return extract_fullcontext(text)

def mecab_dict_index():
    """MeCab字典索引"""
    return "dummy_dict_index"

def unidic_version():
    """UniDic版本"""
    return "dummy_version"

# 常量定义
PHONEMES = [
    'pau', 'A', 'E', 'I', 'N', 'O', 'U', 'a', 'b', 'by', 'ch', 'cl', 'd', 'dy', 'e', 'f', 'g', 'gy', 'h', 'hy', 'i', 'j', 'k', 'ky', 'm', 'my', 'n', 'ny', 'o', 'p', 'py', 'r', 'ry', 's', 'sh', 'sy', 't', 'ts', 'ty', 'u', 'v', 'w', 'y', 'z', 'zy'
]

def phonemes():
    """返回音素列表"""
    return PHONEMES
