"""
临时修复LangSegment兼容性问题的模块
"""

# 全局过滤器设置
_filters = ["zh", "ja", "en", "ko"]

def setfilters(filters):
    """设置语言过滤器"""
    global _filters
    _filters = filters

def getfilters():
    """获取当前过滤器"""
    return _filters

def setLangfilters(filters):
    """设置语言过滤器（别名）"""
    setfilters(filters)

def getLangfilters():
    """获取语言过滤器（别名）"""
    return getfilters()

def getTexts(text):
    """
    简化的文本分割函数
    返回格式: [{"text": "文本", "lang": "语言"}]
    """
    if not text:
        return []
    
    # 简单的语言检测逻辑
    import re
    
    # 检测中文字符
    chinese_pattern = re.compile(r'[\u4e00-\u9fff]+')
    # 检测日文字符（平假名、片假名）
    japanese_pattern = re.compile(r'[\u3040-\u309f\u30a0-\u30ff]+')
    # 检测英文字符
    english_pattern = re.compile(r'[a-zA-Z]+')
    
    # 简单分割逻辑
    segments = []
    current_text = ""
    current_lang = "zh"  # 默认中文
    
    for char in text:
        if chinese_pattern.match(char):
            if current_lang != "zh" and current_text:
                segments.append({"text": current_text.strip(), "lang": current_lang})
                current_text = ""
            current_lang = "zh"
            current_text += char
        elif japanese_pattern.match(char):
            if current_lang != "ja" and current_text:
                segments.append({"text": current_text.strip(), "lang": current_lang})
                current_text = ""
            current_lang = "ja"
            current_text += char
        elif english_pattern.match(char):
            if current_lang != "en" and current_text:
                segments.append({"text": current_text.strip(), "lang": current_lang})
                current_text = ""
            current_lang = "en"
            current_text += char
        else:
            current_text += char
    
    # 添加最后一段
    if current_text.strip():
        segments.append({"text": current_text.strip(), "lang": current_lang})
    
    # 如果没有分割出任何内容，返回整个文本作为中文
    if not segments:
        segments = [{"text": text, "lang": "zh"}]
    
    return segments

def classify(text):
    """分类文本语言"""
    segments = getTexts(text)
    if segments:
        return segments[0]["lang"]
    return "zh"

def getCounts(text):
    """获取各语言字符数量"""
    segments = getTexts(text)
    counts = {}
    for segment in segments:
        lang = segment["lang"]
        if lang in counts:
            counts[lang] += len(segment["text"])
        else:
            counts[lang] = len(segment["text"])
    return counts

def printList(segments):
    """打印分割结果"""
    for i, segment in enumerate(segments):
        print(f"{i}: {segment['lang']} - {segment['text']}")

# 为了兼容性，创建一个LangSegment类
class LangSegment:
    @staticmethod
    def setfilters(filters):
        return setfilters(filters)
    
    @staticmethod
    def getfilters():
        return getfilters()
    
    @staticmethod
    def getTexts(text):
        return getTexts(text)
    
    @staticmethod
    def classify(text):
        return classify(text)
    
    @staticmethod
    def getCounts(text):
        return getCounts(text)
    
    @staticmethod
    def printList(segments):
        return printList(segments)
