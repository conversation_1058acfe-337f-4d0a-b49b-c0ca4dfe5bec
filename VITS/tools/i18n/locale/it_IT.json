{">=3则使用对harvest音高识别的结果使用中值滤波，数值为滤波半径，使用可以削弱哑音": "Se >=3: applica il filtro mediano ai risultati del pitch raccolto. ", "A模型权重": "Peso (w) per il modello A:", "A模型路径": "Percorso per il modello A:", "B模型路径": "Percorso per il modello B:", "E:\\语音音频+标注\\米津玄师\\src": "E:\\语音音频+标注\\米津玄师\\src", "F0曲线文件, 可选, 一行一个音高, 代替默认F0及升降调": "File curva F0 (opzionale). ", "Index Rate": "Tasso di indice", "Onnx导出": "Esporta Onnx", "Onnx输出路径": "Percorso di esportazione Onnx:", "RVC模型路径": "Percorso modello RVC:", "ckpt处理": "Elaborazione ckpt", "harvest进程数": "harvest进程数", "index文件路径不可包含中文": "index文件路径不可包含中文", "pth文件路径不可包含中文": "pth è un'app per il futuro", "rmvpe卡号配置：以-分隔输入使用的不同进程卡号,例如0-0-1使用在卡0上跑2个进程并在卡1上跑1个进程": "rmvpe卡号配置：以-分隔输入使用的不同进程卡号,例如0-0-1使用在卡0上跑2个进程并在卡1上跑1个进程", "step1: 填写实验配置. 实验数据放在logs下, 每个实验一个文件夹, 需手工输入实验名路径, 内含实验配置, 日志, 训练得到的模型文件. ": "Passaggio 1: compilare la configurazione sperimentale. ", "step1:正在处理数据": "Passaggio 1: elaborazione dei dati", "step2:正在提取音高&正在提取特征": "step2:正在提取音高&正在提取特征", "step2a: 自动遍历训练文件夹下所有可解码成音频的文件并进行切片归一化, 在实验目录下生成2个wav文件夹; 暂时只支持单人训练. ": "Passaggio 2a: attraversa automaticamente tutti i file nella cartella di addestramento che possono essere decodificati in audio ed esegui la normalizzazione delle sezioni. ", "step2b: 使用CPU提取音高(如果模型带音高), 使用GPU提取特征(选择卡号)": "Passaggio 2b: utilizzare la CPU per estrarre il tono (se il modello ha il tono), utilizzare la GPU per estrarre le caratteristiche (selezionare l'indice GPU):", "step3: 填写训练设置, 开始训练模型和索引": "Passaggio 3: compilare le impostazioni di addestramento e avviare l'addestramento del modello e dell'indice", "step3a:正在训练模型": "Passaggio 3a: è iniziato l'addestramento del modello", "一键训练": "Addestramento con un clic", "也可批量输入音频文件, 二选一, 优先读文件夹": "也可批量输入音频文件, 二选一, 优先读文件夹", "人声伴奏分离批量处理， 使用UVR5模型。 <br>合格的文件夹路径格式举例： E:\\codes\\py39\\vits_vc_gpu\\白鹭霜华测试样例(去文件管理器地址栏拷就行了)。 <br>模型分为三类： <br>1、保留人声：不带和声的音频选这个，对主人声保留比HP5更好。内置HP2和HP3两个模型，HP3可能轻微漏伴奏但对主人声保留比HP2稍微好一丁点； <br>2、仅保留主人声：带和声的音频选这个，对主人声可能有削弱。内置HP5一个模型； <br> 3、去混响、去延迟模型（by FoxJoy）：<br>  (1)MDX-Net(onnx_dereverb):对于双通道混响是最好的选择，不能去除单通道混响；<br>&emsp;(234)DeEcho:去除延迟效果。Aggressive比Normal去除得更彻底，DeReverb额外去除混响，可去除单声道混响，但是对高频重的板式混响去不干净。<br>去混响/去延迟，附：<br>1、DeEcho-DeReverb模型的耗时是另外2个DeEcho模型的接近2倍；<br>2、MDX-Net-Dereverb模型挺慢的；<br>3、个人推荐的最干净的配置是先MDX-Net再DeEcho-Aggressive。": "Elaborazione batch per la separazione dell'accompagnamento vocale utilizzando il modello UVR5.<br>Esempio di un formato di percorso di cartella valido: D:\\path\\to\\input\\folder (copialo dalla barra degli indirizzi del file manager).<br>Il modello è suddiviso in tre categorie:<br>1. Conserva la voce: scegli questa opzione per l'audio senza armonie. <br>2. Mantieni solo la voce principale: scegli questa opzione per l'audio con armonie. <br>3. Modelli di de-riverbero e de-delay (di FoxJoy):<br>  (1) MDX-Net: la scelta migliore per la rimozione del riverbero stereo ma non può rimuovere il riverbero mono;<br><br>Note di de-riverbero/de-delay:<br>1. Il tempo di elaborazione per il modello DeEcho-DeReverb è circa il doppio rispetto agli altri due modelli DeEcho.<br>2. Il modello MDX-Net-Dereverb è piuttosto lento.<br>3. La configurazione più pulita consigliata consiste nell'applicare prima MDX-Net e poi DeEcho-Aggressive.", "以-分隔输入使用的卡号, 例如   0-1-2   使用卡0和卡1和卡2": "Inserisci gli indici GPU separati da '-', ad esempio 0-1-2 per utilizzare GPU 0, 1 e 2:", "伴奏人声分离&去混响&去回声": "Separazione voce/accompagnamento", "使用模型采样率": "使用模型采样率", "使用设备采样率": "使用设备采样率", "保存名": "Salva nome:", "保存的文件名, 默认空为和源文件同名": "Salva il nome del file (predefinito: uguale al file di origine):", "保存的模型名不带后缀": "Nome del modello salvato (senza estensione):", "保存频率save_every_epoch": "Frequenza di salvataggio (save_every_epoch):", "保护清辅音和呼吸声，防止电音撕裂等artifact，拉满0.5不开启，调低加大保护力度但可能降低索引效果": "Proteggi le consonanti senza voce e i suoni del respiro per evitare artefatti come il tearing nella musica elettronica. ", "修改": "Modificare", "修改模型信息(仅支持weights文件夹下提取的小模型文件)": "Modifica le informazioni sul modello (supportato solo per i file di modello di piccole dimensioni estratti dalla cartella 'weights')", "停止音频转换": "Arresta la conversione audio", "全流程结束！": "Tutti i processi sono stati completati!", "刷新音色列表和索引路径": "Aggiorna l'elenco delle voci e il percorso dell'indice", "加载模型": "Carica <PERSON>lo", "加载预训练底模D路径": "Carica il percorso D del modello base pre-addestrato:", "加载预训练底模G路径": "Carica il percorso G del modello base pre-addestrato:", "单次推理": "单次推理", "卸载音色省显存": "Scarica la voce per risparmiare memoria della GPU:", "变调(整数, 半音数量, 升八度12降八度-12)": "Trasposizione (numero intero, numero di semitoni, alza di un'ottava: 12, abbassa di un'ottava: -12):", "后处理重采样至最终采样率，0为不进行重采样": "Ricampiona l'audio di output in post-elaborazione alla frequenza di campionamento finale. ", "否": "NO", "启用相位声码器": "启用相位声码器", "响应阈值": "Soglia di risposta", "响度因子": "fattore di sonorità", "处理数据": "<PERSON>a dati", "导出Onnx模型": "Esporta modello Onnx", "导出文件格式": "Formato file di esportazione", "常见问题解答": "FAQ (<PERSON><PERSON><PERSON>)", "常规设置": "Impostazioni generali", "开始音频转换": "Avvia la conversione audio", "很遗憾您这没有能用的显卡来支持您训练": "Sfortunatamente, non è disponibile alcuna GPU compatibile per supportare l'addestramento.", "性能设置": "Impostazioni delle prestazioni", "总训练轮数total_epoch": "Epoch totali di addestramento (total_epoch):", "批量推理": "批量推理", "批量转换, 输入待转换音频文件夹, 或上传多个音频文件, 在指定文件夹(默认opt)下输出转换的音频. ": "Conversione massiva. Inserisci il percorso della cartella che contiene i file da convertire o carica più file audio. I file convertiti finiranno nella cartella specificata. (default: opt) ", "指定输出主人声文件夹": "Specifica la cartella di output per le voci:", "指定输出文件夹": "Specifica la cartella di output:", "指定输出非主人声文件夹": "Specificare la cartella di output per l'accompagnamento:", "推理时间(ms):": "Tempo di inferenza (ms):", "推理音色": "Voce di inferenza:", "提取": "Estrai", "提取音高和处理数据使用的CPU进程数": "Numero di processi CPU utilizzati per l'estrazione del tono e l'elaborazione dei dati:", "是": "SÌ", "是否仅保存最新的ckpt文件以节省硬盘空间": "<PERSON><PERSON> solo l'ultimo file '.ckpt' per risparmiare spazio su disco:", "是否在每次保存时间点将最终小模型保存至weights文件夹": "Salva un piccolo modello finale nella cartella \"weights\" in ogni punto di salvataggio:", "是否缓存所有训练集至显存. 10min以下小数据可缓存以加速训练, 大数据缓存会炸显存也加不了多少速": "Memorizza nella cache tutti i set di addestramento nella memoria della GPU. ", "显卡信息": "Informazioni GPU", "本软件以MIT协议开源, 作者不对软件具备任何控制力, 使用软件者、传播软件导出的声音者自负全责. <br>如不认可该条款, 则不能使用或引用软件包内任何代码和文件. 详见根目录<b>LICENSE</b>.": "Questo software è open source con licenza MIT.  <br>Se non si accetta questa clausola, non è possibile utilizzare o fare riferimento a codici e file all'interno del pacchetto software.  <b>Contratto-LICENZA.txt</b> per dettagli.", "查看": "Visualizzazione", "查看模型信息(仅支持weights文件夹下提取的小模型文件)": "Visualizza le informazioni sul modello (supportato solo per file di modello piccoli estratti dalla cartella 'weights')", "检索特征占比": "Rapporto funzionalità di ricerca (controlla la forza dell'accento, troppo alto ha artefatti):", "模型": "<PERSON><PERSON>", "模型推理": "Inferenza del modello", "模型提取(输入logs文件夹下大文件模型路径),适用于训一半不想训了模型没有自动提取保存小文件模型,或者想测试中间模型的情况": "Estrazione del modello (inserire il percorso del modello di file di grandi dimensioni nella cartella \"logs\"). ", "模型是否带音高指导": "Se il modello ha una guida del tono:", "模型是否带音高指导(唱歌一定要, 语音可以不要)": "Se il modello ha una guida del tono (necessario per il canto, facoltativo per il parlato):", "模型是否带音高指导,1是0否": "Se il modello ha una guida del tono (1: sì, 0: no):", "模型版本型号": "Versione dell'architettura del modello:", "模型融合, 可用于测试音色融合": "Model fusion, può essere utilizzato per testare la fusione timbrica", "模型路径": "Percorso al modello:", "每张显卡的batch_size": "Dimensione batch per GPU:", "淡入淡出长度": "Lunghezza dissolvenza", "版本": "Versione", "特征提取": "Estrazione delle caratteristiche", "特征检索库文件路径,为空则使用下拉的选择结果": "Percorso del file di indice delle caratteristiche. ", "男转女推荐+12key, 女转男推荐-12key, 如果音域爆炸导致音色失真也可以自己调整到合适音域. ": "Tonalità +12 consigliata per la conversione da maschio a femmina e tonalità -12 per la conversione da femmina a maschio. ", "目标采样率": "Frequenza di campionamento target:", "算法延迟(ms):": "算法延迟(ms):", "自动检测index路径,下拉式选择(dropdown)": "Rileva automaticamente il percorso dell'indice e seleziona dal menu a tendina:", "融合": "Fusione", "要改的模型信息": "Informazioni sul modello da modificare:", "要置入的模型信息": "Informazioni sul modello da posizionare:", "训练": "Addestramento", "训练模型": "Addestra modello", "训练特征索引": "Addestra indice delle caratteristiche", "训练结束, 您可查看控制台训练日志或实验文件夹下的train.log": "Addestramento completato. ", "请指定说话人id": "Si prega di specificare l'ID del locutore/cantante:", "请选择index文件": "请选择index文件", "请选择pth文件": "请选择pth 文件", "请选择说话人id": "Seleziona ID locutore/cantante:", "转换": "Convertire", "输入实验名": "Inserisci il nome dell'esperimento:", "输入待处理音频文件夹路径": "Immettere il percorso della cartella audio da elaborare:", "输入待处理音频文件夹路径(去文件管理器地址栏拷就行了)": "Immettere il percorso della cartella audio da elaborare (copiarlo dalla barra degli indirizzi del file manager):", "输入待处理音频文件路径(默认是正确格式示例)": "Immettere il percorso del file audio da elaborare (l'impostazione predefinita è l'esempio di formato corretto):", "输入源音量包络替换输出音量包络融合比例，越靠近1越使用输出包络": "Regola il ridimensionamento dell'inviluppo del volume. ", "输入监听": "输入监听", "输入训练文件夹路径": "Inserisci il percorso della cartella di addestramento:", "输入设备": "Dispositivo di input", "输入降噪": "Riduzione del rumore in ingresso", "输出信息": "Informazioni sull'uscita", "输出变声": "输出变声", "输出设备": "Dispositivo di uscita", "输出降噪": "Riduzione del rumore in uscita", "输出音频(右下角三个点,点了可以下载)": "Esporta audio (clicca sui tre puntini in basso a destra per scaricarlo)", "选择.index文件": "Seleziona il file .index", "选择.pth文件": "Seleziona il file .pth", "选择音高提取算法,输入歌声可用pm提速,harvest低音好但巨慢无比,crepe效果好但吃GPU": "选择音高提取算法,输入歌声可用pm提速,harvest低音好但巨慢无比,crepe效果好但吃GPU", "选择音高提取算法,输入歌声可用pm提速,harvest低音好但巨慢无比,crepe效果好但吃GPU,rmvpe效果最好且微吃GPU": "Seleziona l'algoritmo di estrazione del tono (\"pm\": estrazione più veloce ma risultato di qualità inferiore; \"harvest\": bassi migliori ma estremamente lenti; \"crepe\": qualità migliore ma utilizzo intensivo della GPU):", "选择音高提取算法:输入歌声可用pm提速,高质量语音但CPU差可用dio提速,harvest质量更好但慢,rmvpe效果最好且微吃CPU/GPU": "选择音高提取算法:输入歌声可用pm提速,高质量语音但CPU差可用dio提速,harvest质量更好但慢,rmvpe效果最好且微吃CPU/GPU", "采样率:": "采样率:", "采样长度": "Lunghezza del campione", "重载设备列表": "Ricaricare l'elenco dei dispositivi", "音调设置": "Impostazioni del tono", "音频设备(请使用同种类驱动)": "Dispositivo audio (utiliz<PERSON><PERSON> lo stesso tipo di driver)", "音高算法": "音高算法", "额外推理时长": "Tempo di inferenza extra"}