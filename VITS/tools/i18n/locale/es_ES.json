{">=3则使用对harvest音高识别的结果使用中值滤波，数值为滤波半径，使用可以削弱哑音": "Si es >=3, entonces use el resultado del reconocimiento de tono de 'harvest' con filtro de mediana, el valor es el radio del filtro, su uso puede debilitar el sonido sordo", "A模型权重": "Un peso modelo para el modelo A.", "A模型路径": "Modelo A ruta.", "B模型路径": "Modelo B ruta.", "E:\\语音音频+标注\\米津玄师\\src": "E:\\语音音频+标注\\米津玄师\\src", "F0曲线文件, 可选, 一行一个音高, 代替默认F0及升降调": "Archivo de curva F0, opcional, un tono por línea, en lugar de F0 predeterminado y cambio de tono", "Index Rate": "<PERSON><PERSON>", "Onnx导出": "Exportar Onnx", "Onnx输出路径": "<PERSON><PERSON> de salida <PERSON>", "RVC模型路径": "Ruta del modelo RVC", "ckpt处理": "Procesamiento de recibos", "harvest进程数": "Número de procesos", "index文件路径不可包含中文": "La ruta del archivo .index no debe contener caracteres chinos.", "pth文件路径不可包含中文": "La ruta del archivo .pth no debe contener caracteres chinos.", "rmvpe卡号配置：以-分隔输入使用的不同进程卡号,例如0-0-1使用在卡0上跑2个进程并在卡1上跑1个进程": "Separe los números de identificación de la GPU con '-' al ingresarlos. <PERSON><PERSON> e<PERSON><PERSON><PERSON>, '0-1-2' significa usar GPU 0, GPU 1 y GPU 2.", "step1: 填写实验配置. 实验数据放在logs下, 每个实验一个文件夹, 需手工输入实验名路径, 内含实验配置, 日志, 训练得到的模型文件. ": "Paso 1: Complete la configuración del experimento. Los datos del experimento se almacenan en el directorio 'logs', con cada experimento en una carpeta separada. La ruta del nombre del experimento debe ingresarse manualmente y debe contener la configuración del experimento, los registros y los archivos del modelo entrenado.", "step1:正在处理数据": "Paso 1: <PERSON><PERSON><PERSON><PERSON>", "step2:正在提取音高&正在提取特征": "Paso 2: Extracción del tono y extracción de características", "step2a: 自动遍历训练文件夹下所有可解码成音频的文件并进行切片归一化, 在实验目录下生成2个wav文件夹; 暂时只支持单人训练. ": "Paso 2a: Recorra automáticamente la carpeta de capacitación y corte y normalice todos los archivos de audio que se pueden decodificar en audio. Se generarán dos carpetas 'wav' en el directorio del experimento. Actualmente, solo se admite la capacitación de una sola persona.", "step2b: 使用CPU提取音高(如果模型带音高), 使用GPU提取特征(选择卡号)": "Paso 2b: Use la CPU para extraer el tono (si el modelo tiene guía de tono) y la GPU para extraer características (seleccione el número de tarjeta).", "step3: 填写训练设置, 开始训练模型和索引": "Paso 3: Complete la configuración de entrenamiento y comience a entrenar el modelo y el índice.", "step3a:正在训练模型": "Paso 3a: <PERSON><PERSON><PERSON><PERSON> el modelo", "一键训练": "Entrenamiento con un clic", "也可批量输入音频文件, 二选一, 优先读文件夹": "También se pueden importar varios archivos de audio. Si existe una ruta de carpeta, esta entrada se ignora.", "人声伴奏分离批量处理， 使用UVR5模型。 <br>合格的文件夹路径格式举例： E:\\codes\\py39\\vits_vc_gpu\\白鹭霜华测试样例(去文件管理器地址栏拷就行了)。 <br>模型分为三类： <br>1、保留人声：不带和声的音频选这个，对主人声保留比HP5更好。内置HP2和HP3两个模型，HP3可能轻微漏伴奏但对主人声保留比HP2稍微好一丁点； <br>2、仅保留主人声：带和声的音频选这个，对主人声可能有削弱。内置HP5一个模型； <br> 3、去混响、去延迟模型（by FoxJoy）：<br>  (1)MDX-Net(onnx_dereverb):对于双通道混响是最好的选择，不能去除单通道混响；<br>&emsp;(234)DeEcho:去除延迟效果。Aggressive比Normal去除得更彻底，DeReverb额外去除混响，可去除单声道混响，但是对高频重的板式混响去不干净。<br>去混响/去延迟，附：<br>1、DeEcho-DeReverb模型的耗时是另外2个DeEcho模型的接近2倍；<br>2、MDX-Net-Dereverb模型挺慢的；<br>3、个人推荐的最干净的配置是先MDX-Net再DeEcho-Aggressive。": "Procesamiento por lotes para la separación de acompañamiento vocal utilizando el modelo UVR5.<br>Ejemplo de formato de ruta de carpeta válido: D:\\ruta\\a\\la\\carpeta\\de\\entrada (copiar desde la barra de direcciones del administrador de archivos).<br>El modelo se divide en tres categorías:<br>1. Preservar voces: Elija esta opción para audio sin armonías. Preserva las voces mejor que HP5. Incluye dos modelos incorporados: HP2 y HP3. HP3 puede filtrar ligeramente el acompañamiento pero conserva las voces un poco mejor que HP2.<br>2. Preservar solo voces principales: Elija esta opción para audio con armonías. Puede debilitar las voces principales. Incluye un modelo incorporado: HP5.<br>3. Modelos de des-reverberación y des-retardo (por FoxJoy):<br>  (1) MDX-Net: La mejor opción para la eliminación de reverberación estéreo pero no puede eliminar la reverberación mono;<br>&emsp;(234) DeEcho: Elimina efectos de retardo. El modo Agresivo elimina más a fondo que el modo Normal. DeReverb adicionalmente elimina la reverberación y puede eliminar la reverberación mono, pero no muy efectivamente para contenido de alta frecuencia fuertemente reverberado.<br>Notas de des-reverberación/des-retardo:<br>1. El tiempo de procesamiento para el modelo DeEcho-DeReverb es aproximadamente el doble que los otros dos modelos DeEcho.<br>2. El modelo MDX-Net-Dereverb es bastante lento.<br>3. La configuración más limpia recomendada es aplicar primero MDX-Net y luego DeEcho-Agresivo.", "以-分隔输入使用的卡号, 例如   0-1-2   使用卡0和卡1和卡2": "Separe los números de identificación de la GPU con '-' al ingresarlos. <PERSON><PERSON> e<PERSON><PERSON><PERSON>, '0-1-2' significa usar GPU 0, GPU 1 y GPU 2.", "伴奏人声分离&去混响&去回声": "Separación de voz acompañante & eliminación de reverberación & eco", "使用模型采样率": "使用模型采样率", "使用设备采样率": "使用设备采样率", "保存名": "Guardar nombre", "保存的文件名, 默认空为和源文件同名": "Nombre del archivo que se guardará, el valor predeterminado es el mismo que el nombre del archivo de origen", "保存的模型名不带后缀": "Nombre del modelo guardado sin extensión.", "保存频率save_every_epoch": "Frecuencia de guardado (save_every_epoch)", "保护清辅音和呼吸声，防止电音撕裂等artifact，拉满0.5不开启，调低加大保护力度但可能降低索引效果": "Proteger las consonantes claras y la respiración, prevenir artefactos como la distorsión de sonido electrónico, 0.5 no está activado, reducir aumentará la protección pero puede reducir el efecto del índice", "修改": "Modificar", "修改模型信息(仅支持weights文件夹下提取的小模型文件)": "Modificar la información del modelo (solo admite archivos de modelos pequeños extraídos en la carpeta weights)", "停止音频转换": "Detener la conversión de audio", "全流程结束！": "¡Todo el proceso ha terminado!", "刷新音色列表和索引路径": "Actualizar la lista de modelos e índice de rutas", "加载模型": "<PERSON><PERSON> modelo", "加载预训练底模D路径": "Cargue la ruta del modelo D base pre-entrenada.", "加载预训练底模G路径": "Cargue la ruta del modelo G base pre-entrenada.", "单次推理": "单次推理", "卸载音色省显存": "Descargue la voz para ahorrar memoria GPU", "变调(整数, 半音数量, 升八度12降八度-12)": "Cambio de tono (entero, número de semitonos, subir una octava +12 o bajar una octava -12)", "后处理重采样至最终采样率，0为不进行重采样": "Remuestreo posterior al proceso a la tasa de muestreo final, 0 significa no remuestrear", "否": "No", "启用相位声码器": "启用相位声码器", "响应阈值": "Umbral de respuesta", "响度因子": "factor de sonoridad", "处理数据": "Procesar da<PERSON>", "导出Onnx模型": "Exportar modelo Onnx", "导出文件格式": "Formato de archivo de exportación", "常见问题解答": "Preguntas frecuentes", "常规设置": "Configuración general", "开始音频转换": "Iniciar conversión de audio", "很遗憾您这没有能用的显卡来支持您训练": "Lamentablemente, no tiene una tarjeta gráfica adecuada para soportar su entrenamiento", "性能设置": "Configuración de rendimiento", "总训练轮数total_epoch": "Total de épocas de entrenamiento (total_epoch)", "批量推理": "批量推理", "批量转换, 输入待转换音频文件夹, 或上传多个音频文件, 在指定文件夹(默认opt)下输出转换的音频. ": "Conversión por lotes, ingrese la carpeta que contiene los archivos de audio para convertir o cargue varios archivos de audio. El audio convertido se emitirá en la carpeta especificada (opción predeterminada).", "指定输出主人声文件夹": "Especifique la carpeta de salida para la voz principal", "指定输出文件夹": "Especificar carpeta de salida", "指定输出非主人声文件夹": "Especifique la carpeta de salida para las voces no principales", "推理时间(ms):": "<PERSON><PERSON><PERSON> tiempo (ms):", "推理音色": "inferencia de voz", "提取": "Extraer", "提取音高和处理数据使用的CPU进程数": "Número de procesos de CPU utilizados para extraer el tono y procesar los datos", "是": "Sí", "是否仅保存最新的ckpt文件以节省硬盘空间": "Guardar solo el archivo ckpt más reciente para ahorrar espacio en disco", "是否在每次保存时间点将最终小模型保存至weights文件夹": "Guardar pequeño modelo final en la carpeta 'weights' en cada punto de guardado", "是否缓存所有训练集至显存. 10min以下小数据可缓存以加速训练, 大数据缓存会炸显存也加不了多少速": "Si almacenar en caché todos los conjuntos de entrenamiento en la memoria de la GPU. Los conjuntos de datos pequeños (menos de 10 minutos) se pueden almacenar en caché para acelerar el entrenamiento, pero el almacenamiento en caché de conjuntos de datos grandes puede causar errores de memoria en la GPU y no aumenta la velocidad de manera significativa.", "显卡信息": "información de la GPU", "本软件以MIT协议开源, 作者不对软件具备任何控制力, 使用软件者、传播软件导出的声音者自负全责. <br>如不认可该条款, 则不能使用或引用软件包内任何代码和文件. 详见根目录<b>LICENSE</b>.": "Este software es de código abierto bajo la licencia MIT, el autor no tiene ningún control sobre el software, y aquellos que usan el software y difunden los sonidos exportados por el software son los únicos responsables.<br>Si no está de acuerdo con esta cláusula , no puede utilizar ni citar ningún código ni archivo del paquete de software Consulte el directorio raíz <b>Agreement-LICENSE.txt</b> para obtener más información.", "查看": "<PERSON>er", "查看模型信息(仅支持weights文件夹下提取的小模型文件)": "Ver información del modelo (solo aplicable a archivos de modelos pequeños extraídos de la carpeta 'pesos')", "检索特征占比": "Proporción de función de búsqueda", "模型": "<PERSON><PERSON>", "模型推理": "inferencia del modelo", "模型提取(输入logs文件夹下大文件模型路径),适用于训一半不想训了模型没有自动提取保存小文件模型,或者想测试中间模型的情况": "Extracción de modelo (ingrese la ruta de un archivo de modelo grande en la carpeta 'logs'), aplicable cuando desea extraer un archivo de modelo pequeño después de entrenar a mitad de camino y no se guardó automáticamente, o cuando desea probar un modelo intermedio", "模型是否带音高指导": "Si el modelo tiene guía de tono.", "模型是否带音高指导(唱歌一定要, 语音可以不要)": "Si el modelo tiene guía de tono (necesaria para cantar, pero no para hablar)", "模型是否带音高指导,1是0否": "Si el modelo tiene guía de tono, 1 para sí, 0 para no", "模型版本型号": "Versión y modelo del modelo", "模型融合, 可用于测试音色融合": "Fusión de modelos, se puede utilizar para fusionar diferentes voces", "模型路径": "Ruta del modelo", "每张显卡的batch_size": "Tam<PERSON><PERSON> del lote (batch_size) por tarjeta gráfica", "淡入淡出长度": "Duración del fundido de entrada/salida", "版本": "Versión", "特征提取": "Extracción de características", "特征检索库文件路径,为空则使用下拉的选择结果": "Ruta del archivo de la biblioteca de características, si está vacío, se utilizará el resultado de la selección desplegable", "男转女推荐+12key, 女转男推荐-12key, 如果音域爆炸导致音色失真也可以自己调整到合适音域. ": "Tecla +12 recomendada para conversión de voz de hombre a mujer, tecla -12 para conversión de voz de mujer a hombre. Si el rango de tono es demasiado amplio y causa distorsión, ajústelo usted mismo a un rango adecuado.", "目标采样率": "Tasa de muestreo objetivo", "算法延迟(ms):": "算法延迟(ms):", "自动检测index路径,下拉式选择(dropdown)": "Detección automática de la ruta del índice, selección desplegable (dropdown)", "融合": "Fusión", "要改的模型信息": "Información del modelo a modificar", "要置入的模型信息": "Información del modelo a colocar.", "训练": "Entrenamiento", "训练模型": "<PERSON><PERSON><PERSON>", "训练特征索引": "Índice de características", "训练结束, 您可查看控制台训练日志或实验文件夹下的train.log": "Entrenamiento finalizado, puede ver el registro de entrenamiento en la consola o en el archivo train.log en la carpeta del experimento", "请指定说话人id": "ID del modelo", "请选择index文件": "Seleccione el archivo .index", "请选择pth文件": "Seleccione el archivo .pth", "请选择说话人id": "Seleccione una identificación de altavoz", "转换": "Conversión", "输入实验名": "Ingrese el nombre del modelo", "输入待处理音频文件夹路径": "Ingrese la ruta a la carpeta de audio que se procesará", "输入待处理音频文件夹路径(去文件管理器地址栏拷就行了)": "Ingrese la ruta a la carpeta de audio que se procesará (simplemente cópiela desde la barra de direcciones del administrador de archivos)", "输入待处理音频文件路径(默认是正确格式示例)": "Ingrese la ruta del archivo del audio que se procesará (el formato predeterminado es el ejemplo correcto)", "输入源音量包络替换输出音量包络融合比例，越靠近1越使用输出包络": "Proporción de fusión para reemplazar el sobre de volumen de entrada con el sobre de volumen de salida, cuanto más cerca de 1, más se utiliza el sobre de salida", "输入监听": "输入监听", "输入训练文件夹路径": "Introduzca la ruta de la carpeta de entrenamiento", "输入设备": "Dispositivo de entrada", "输入降噪": "Reducción de ruido de entrada", "输出信息": "Información de salida", "输出变声": "输出变声", "输出设备": "Dispositivo de salida", "输出降噪": "Reducción de ruido de salida", "输出音频(右下角三个点,点了可以下载)": "Salida de audio (haga clic en los tres puntos en la esquina inferior derecha para descargar)", "选择.index文件": "Seleccione el archivo .index", "选择.pth文件": "Seleccione el archivo .pth", "选择音高提取算法,输入歌声可用pm提速,harvest低音好但巨慢无比,crepe效果好但吃GPU": "Seleccione el algoritmo de extracción de tono, las voces de entrada se pueden acelerar con pm, harvest tiene buenos graves pero es muy lento, crepe es bueno pero se come las GPUs", "选择音高提取算法,输入歌声可用pm提速,harvest低音好但巨慢无比,crepe效果好但吃GPU,rmvpe效果最好且微吃GPU": "Seleccione el algoritmo de extracción de tono, use 'pm' para acelerar la entrada de canto, 'harvest' es bueno para los graves pero extremadamente lento, 'crepe' tiene buenos resultados pero consume GPU", "选择音高提取算法:输入歌声可用pm提速,高质量语音但CPU差可用dio提速,harvest质量更好但慢,rmvpe效果最好且微吃CPU/GPU": "Seleccione el algoritmo de extracción de tono: la canción de entrada se puede acelerar con pm, la voz de alta calidad pero CPU pobre se puede acelerar con dio, harvest es mejor pero más lento, rmvpe es el mejor y se come ligeramente la CPU/GPU", "采样率:": "采样率:", "采样长度": "Longitud de muestreo", "重载设备列表": "Actualizar lista de dispositivos", "音调设置": "A<PERSON>ste de tono", "音频设备(请使用同种类驱动)": "Dispositivo de audio (utilice el mismo tipo de controlador)", "音高算法": "Algoritmo de tono", "额外推理时长": "Tiempo de inferencia adicional"}