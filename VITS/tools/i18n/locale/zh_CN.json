{">=3则使用对harvest音高识别的结果使用中值滤波，数值为滤波半径，使用可以削弱哑音": ">=3则使用对harvest音高识别的结果使用中值滤波，数值为滤波半径，使用可以削弱哑音", "A模型权重": "A模型权重", "A模型路径": "A模型路径", "B模型路径": "B模型路径", "E:\\语音音频+标注\\米津玄师\\src": "E:\\语音音频+标注\\米津玄师\\src", "F0曲线文件, 可选, 一行一个音高, 代替默认F0及升降调": "F0曲线文件, 可选, 一行一个音高, 代替默认F0及升降调", "Index Rate": "Index Rate", "Onnx导出": "Onnx导出", "Onnx输出路径": "Onnx输出路径", "RVC模型路径": "RVC模型路径", "ckpt处理": "ckpt处理", "harvest进程数": "harvest进程数", "index文件路径不可包含中文": "index文件路径不可包含中文", "pth文件路径不可包含中文": "pth文件路径不可包含中文", "rmvpe卡号配置：以-分隔输入使用的不同进程卡号,例如0-0-1使用在卡0上跑2个进程并在卡1上跑1个进程": "rmvpe卡号配置：以-分隔输入使用的不同进程卡号,例如0-0-1使用在卡0上跑2个进程并在卡1上跑1个进程", "step1: 填写实验配置. 实验数据放在logs下, 每个实验一个文件夹, 需手工输入实验名路径, 内含实验配置, 日志, 训练得到的模型文件. ": "step1: 填写实验配置. 实验数据放在logs下, 每个实验一个文件夹, 需手工输入实验名路径, 内含实验配置, 日志, 训练得到的模型文件. ", "step1:正在处理数据": "step1:正在处理数据", "step2:正在提取音高&正在提取特征": "step2:正在提取音高&正在提取特征", "step2a: 自动遍历训练文件夹下所有可解码成音频的文件并进行切片归一化, 在实验目录下生成2个wav文件夹; 暂时只支持单人训练. ": "step2a: 自动遍历训练文件夹下所有可解码成音频的文件并进行切片归一化, 在实验目录下生成2个wav文件夹; 暂时只支持单人训练. ", "step2b: 使用CPU提取音高(如果模型带音高), 使用GPU提取特征(选择卡号)": "step2b: 使用CPU提取音高(如果模型带音高), 使用GPU提取特征(选择卡号)", "step3: 填写训练设置, 开始训练模型和索引": "step3: 填写训练设置, 开始训练模型和索引", "step3a:正在训练模型": "step3a:正在训练模型", "一键训练": "一键训练", "也可批量输入音频文件, 二选一, 优先读文件夹": "也可批量输入音频文件, 二选一, 优先读文件夹", "人声伴奏分离批量处理， 使用UVR5模型。 <br>合格的文件夹路径格式举例： E:\\codes\\py39\\vits_vc_gpu\\白鹭霜华测试样例(去文件管理器地址栏拷就行了)。 <br>模型分为三类： <br>1、保留人声：不带和声的音频选这个，对主人声保留比HP5更好。内置HP2和HP3两个模型，HP3可能轻微漏伴奏但对主人声保留比HP2稍微好一丁点； <br>2、仅保留主人声：带和声的音频选这个，对主人声可能有削弱。内置HP5一个模型； <br> 3、去混响、去延迟模型（by FoxJoy）：<br>  (1)MDX-Net(onnx_dereverb):对于双通道混响是最好的选择，不能去除单通道混响；<br>&emsp;(234)DeEcho:去除延迟效果。Aggressive比Normal去除得更彻底，DeReverb额外去除混响，可去除单声道混响，但是对高频重的板式混响去不干净。<br>去混响/去延迟，附：<br>1、DeEcho-DeReverb模型的耗时是另外2个DeEcho模型的接近2倍；<br>2、MDX-Net-Dereverb模型挺慢的；<br>3、个人推荐的最干净的配置是先MDX-Net再DeEcho-Aggressive。": "人声伴奏分离批量处理， 使用UVR5模型。 <br>合格的文件夹路径格式举例： E:\\codes\\py39\\vits_vc_gpu\\白鹭霜华测试样例(去文件管理器地址栏拷就行了)。 <br>模型分为三类： <br>1、保留人声：不带和声的音频选这个，对主人声保留比HP5更好。内置HP2和HP3两个模型，HP3可能轻微漏伴奏但对主人声保留比HP2稍微好一丁点； <br>2、仅保留主人声：带和声的音频选这个，对主人声可能有削弱。内置HP5一个模型； <br> 3、去混响、去延迟模型（by FoxJoy）：<br>  (1)MDX-Net(onnx_dereverb):对于双通道混响是最好的选择，不能去除单通道混响；<br>&emsp;(234)DeEcho:去除延迟效果。Aggressive比Normal去除得更彻底，DeReverb额外去除混响，可去除单声道混响，但是对高频重的板式混响去不干净。<br>去混响/去延迟，附：<br>1、DeEcho-DeReverb模型的耗时是另外2个DeEcho模型的接近2倍；<br>2、MDX-Net-Dereverb模型挺慢的；<br>3、个人推荐的最干净的配置是先MDX-Net再DeEcho-Aggressive。", "以-分隔输入使用的卡号, 例如   0-1-2   使用卡0和卡1和卡2": "以-分隔输入使用的卡号, 例如   0-1-2   使用卡0和卡1和卡2", "伴奏人声分离&去混响&去回声": "伴奏人声分离&去混响&去回声", "使用模型采样率": "使用模型采样率", "使用设备采样率": "使用设备采样率", "保存名": "保存名", "保存的文件名, 默认空为和源文件同名": "保存的文件名, 默认空为和源文件同名", "保存的模型名不带后缀": "保存的模型名不带后缀", "保存频率save_every_epoch": "保存频率save_every_epoch", "保护清辅音和呼吸声，防止电音撕裂等artifact，拉满0.5不开启，调低加大保护力度但可能降低索引效果": "保护清辅音和呼吸声，防止电音撕裂等artifact，拉满0.5不开启，调低加大保护力度但可能降低索引效果", "修改": "修改", "修改模型信息(仅支持weights文件夹下提取的小模型文件)": "修改模型信息(仅支持weights文件夹下提取的小模型文件)", "停止音频转换": "停止音频转换", "全流程结束！": "全流程结束！", "刷新音色列表和索引路径": "刷新音色列表和索引路径", "加载模型": "加载模型", "加载预训练底模D路径": "加载预训练底模D路径", "加载预训练底模G路径": "加载预训练底模G路径", "单次推理": "单次推理", "卸载音色省显存": "卸载音色省显存", "变调(整数, 半音数量, 升八度12降八度-12)": "变调(整数, 半音数量, 升八度12降八度-12)", "后处理重采样至最终采样率，0为不进行重采样": "后处理重采样至最终采样率，0为不进行重采样", "否": "否", "启用相位声码器": "启用相位声码器", "响应阈值": "响应阈值", "响度因子": "响度因子", "处理数据": "处理数据", "导出Onnx模型": "导出Onnx模型", "导出文件格式": "导出文件格式", "常见问题解答": "常见问题解答", "常规设置": "常规设置", "开始音频转换": "开始音频转换", "很遗憾您这没有能用的显卡来支持您训练": "很遗憾您这没有能用的显卡来支持您训练", "性能设置": "性能设置", "总训练轮数total_epoch": "总训练轮数total_epoch", "批量推理": "批量推理", "批量转换, 输入待转换音频文件夹, 或上传多个音频文件, 在指定文件夹(默认opt)下输出转换的音频. ": "批量转换, 输入待转换音频文件夹, 或上传多个音频文件, 在指定文件夹(默认opt)下输出转换的音频. ", "指定输出主人声文件夹": "指定输出主人声文件夹", "指定输出文件夹": "指定输出文件夹", "指定输出非主人声文件夹": "指定输出非主人声文件夹", "推理时间(ms):": "推理时间(ms):", "推理音色": "推理音色", "提取": "提取", "提取音高和处理数据使用的CPU进程数": "提取音高和处理数据使用的CPU进程数", "是": "是", "是否仅保存最新的ckpt文件以节省硬盘空间": "是否仅保存最新的ckpt文件以节省硬盘空间", "是否在每次保存时间点将最终小模型保存至weights文件夹": "是否在每次保存时间点将最终小模型保存至weights文件夹", "是否缓存所有训练集至显存. 10min以下小数据可缓存以加速训练, 大数据缓存会炸显存也加不了多少速": "是否缓存所有训练集至显存. 10min以下小数据可缓存以加速训练, 大数据缓存会炸显存也加不了多少速", "显卡信息": "显卡信息", "本软件以MIT协议开源, 作者不对软件具备任何控制力, 使用软件者、传播软件导出的声音者自负全责. <br>如不认可该条款, 则不能使用或引用软件包内任何代码和文件. 详见根目录<b>LICENSE</b>.": "本软件以MIT协议开源, 作者不对软件具备任何控制力, 使用软件者、传播软件导出的声音者自负全责. <br>如不认可该条款, 则不能使用或引用软件包内任何代码和文件. 详见根目录<b>LICENSE</b>.", "查看": "查看", "查看模型信息(仅支持weights文件夹下提取的小模型文件)": "查看模型信息(仅支持weights文件夹下提取的小模型文件)", "检索特征占比": "检索特征占比", "模型": "模型", "模型推理": "模型推理", "模型提取(输入logs文件夹下大文件模型路径),适用于训一半不想训了模型没有自动提取保存小文件模型,或者想测试中间模型的情况": "模型提取(输入logs文件夹下大文件模型路径),适用于训一半不想训了模型没有自动提取保存小文件模型,或者想测试中间模型的情况", "模型是否带音高指导": "模型是否带音高指导", "模型是否带音高指导(唱歌一定要, 语音可以不要)": "模型是否带音高指导(唱歌一定要, 语音可以不要)", "模型是否带音高指导,1是0否": "模型是否带音高指导,1是0否", "模型版本型号": "模型版本型号", "模型融合, 可用于测试音色融合": "模型融合, 可用于测试音色融合", "模型路径": "模型路径", "每张显卡的batch_size": "每张显卡的batch_size", "淡入淡出长度": "淡入淡出长度", "版本": "版本", "特征提取": "特征提取", "特征检索库文件路径,为空则使用下拉的选择结果": "特征检索库文件路径,为空则使用下拉的选择结果", "男转女推荐+12key, 女转男推荐-12key, 如果音域爆炸导致音色失真也可以自己调整到合适音域. ": "男转女推荐+12key, 女转男推荐-12key, 如果音域爆炸导致音色失真也可以自己调整到合适音域. ", "目标采样率": "目标采样率", "算法延迟(ms):": "算法延迟(ms):", "自动检测index路径,下拉式选择(dropdown)": "自动检测index路径,下拉式选择(dropdown)", "融合": "融合", "要改的模型信息": "要改的模型信息", "要置入的模型信息": "要置入的模型信息", "训练": "训练", "训练模型": "训练模型", "训练特征索引": "训练特征索引", "训练结束, 您可查看控制台训练日志或实验文件夹下的train.log": "训练结束, 您可查看控制台训练日志或实验文件夹下的train.log", "请指定说话人id": "请指定说话人id", "请选择index文件": "请选择index文件", "请选择pth文件": "请选择pth文件", "请选择说话人id": "请选择说话人id", "转换": "转换", "输入实验名": "输入实验名", "输入待处理音频文件夹路径": "输入待处理音频文件夹路径", "输入待处理音频文件夹路径(去文件管理器地址栏拷就行了)": "输入待处理音频文件夹路径(去文件管理器地址栏拷就行了)", "输入待处理音频文件路径(默认是正确格式示例)": "输入待处理音频文件路径(默认是正确格式示例)", "输入源音量包络替换输出音量包络融合比例，越靠近1越使用输出包络": "输入源音量包络替换输出音量包络融合比例，越靠近1越使用输出包络", "输入监听": "输入监听", "输入训练文件夹路径": "输入训练文件夹路径", "输入设备": "输入设备", "输入降噪": "输入降噪", "输出信息": "输出信息", "输出变声": "输出变声", "输出设备": "输出设备", "输出降噪": "输出降噪", "输出音频(右下角三个点,点了可以下载)": "输出音频(右下角三个点,点了可以下载)", "选择.index文件": "选择.index文件", "选择.pth文件": "选择.pth文件", "选择音高提取算法,输入歌声可用pm提速,harvest低音好但巨慢无比,crepe效果好但吃GPU": "选择音高提取算法,输入歌声可用pm提速,harvest低音好但巨慢无比,crepe效果好但吃GPU", "选择音高提取算法,输入歌声可用pm提速,harvest低音好但巨慢无比,crepe效果好但吃GPU,rmvpe效果最好且微吃GPU": "选择音高提取算法,输入歌声可用pm提速,harvest低音好但巨慢无比,crepe效果好但吃GPU,rmvpe效果最好且微吃GPU", "选择音高提取算法:输入歌声可用pm提速,高质量语音但CPU差可用dio提速,harvest质量更好但慢,rmvpe效果最好且微吃CPU/GPU": "选择音高提取算法:输入歌声可用pm提速,高质量语音但CPU差可用dio提速,harvest质量更好但慢,rmvpe效果最好且微吃CPU/GPU", "采样率:": "采样率:", "采样长度": "采样长度", "重载设备列表": "重载设备列表", "音调设置": "音调设置", "音频设备(请使用同种类驱动)": "音频设备(请使用同种类驱动)", "音高算法": "音高算法", "额外推理时长": "额外推理时长"}