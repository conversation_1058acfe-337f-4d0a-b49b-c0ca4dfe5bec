{">=3则使用对harvest音高识别的结果使用中值滤波，数值为滤波半径，使用可以削弱哑音": ">=3則使用對harvest音高識別的結果使用中值濾波，數值為濾波半徑，使用可以削弱啞音", "A模型权重": "A模型權重", "A模型路径": "A模型路徑", "B模型路径": "B模型路徑", "E:\\语音音频+标注\\米津玄师\\src": "E:\\语音音频+标注\\米津玄师\\src", "F0曲线文件, 可选, 一行一个音高, 代替默认F0及升降调": "F0曲線檔案，可選，一行一個音高，代替預設的F0及升降調", "Index Rate": "Index Rate", "Onnx导出": "Onnx导出", "Onnx输出路径": "Onnx输出路径", "RVC模型路径": "RVC模型路径", "ckpt处理": "ckpt處理", "harvest进程数": "harvest進程數", "index文件路径不可包含中文": "index文件路径不可包含中文", "pth文件路径不可包含中文": "pth文件路径不可包含中文", "rmvpe卡号配置：以-分隔输入使用的不同进程卡号,例如0-0-1使用在卡0上跑2个进程并在卡1上跑1个进程": "rmvpe卡號配置：以-分隔輸入使用的不同進程卡號,例如0-0-1使用在卡0上跑2個進程並在卡1上跑1個進程", "step1: 填写实验配置. 实验数据放在logs下, 每个实验一个文件夹, 需手工输入实验名路径, 内含实验配置, 日志, 训练得到的模型文件. ": "step1：填寫實驗配置。實驗數據放在logs下，每個實驗一個資料夾，需手動輸入實驗名路徑，內含實驗配置、日誌、訓練得到的模型檔案。", "step1:正在处理数据": "step1:正在处理数据", "step2:正在提取音高&正在提取特征": "step2:正在提取音高&正在提取特征", "step2a: 自动遍历训练文件夹下所有可解码成音频的文件并进行切片归一化, 在实验目录下生成2个wav文件夹; 暂时只支持单人训练. ": "step2a：自動遍歷訓練資料夾下所有可解碼成音頻的檔案並進行切片歸一化，在實驗目錄下生成2個wav資料夾；暫時只支援單人訓練。", "step2b: 使用CPU提取音高(如果模型带音高), 使用GPU提取特征(选择卡号)": "步驟2b: 使用CPU提取音高(如果模型帶音高), 使用GPU提取特徵(選擇卡號)", "step3: 填写训练设置, 开始训练模型和索引": "步驟3: 填寫訓練設定, 開始訓練模型和索引", "step3a:正在训练模型": "step3a:正在训练模型", "一键训练": "一鍵訓練", "也可批量输入音频文件, 二选一, 优先读文件夹": "也可批量输入音频文件, 二选一, 优先读文件夹", "人声伴奏分离批量处理， 使用UVR5模型。 <br>合格的文件夹路径格式举例： E:\\codes\\py39\\vits_vc_gpu\\白鹭霜华测试样例(去文件管理器地址栏拷就行了)。 <br>模型分为三类： <br>1、保留人声：不带和声的音频选这个，对主人声保留比HP5更好。内置HP2和HP3两个模型，HP3可能轻微漏伴奏但对主人声保留比HP2稍微好一丁点； <br>2、仅保留主人声：带和声的音频选这个，对主人声可能有削弱。内置HP5一个模型； <br> 3、去混响、去延迟模型（by FoxJoy）：<br>  (1)MDX-Net(onnx_dereverb):对于双通道混响是最好的选择，不能去除单通道混响；<br>&emsp;(234)DeEcho:去除延迟效果。Aggressive比Normal去除得更彻底，DeReverb额外去除混响，可去除单声道混响，但是对高频重的板式混响去不干净。<br>去混响/去延迟，附：<br>1、DeEcho-DeReverb模型的耗时是另外2个DeEcho模型的接近2倍；<br>2、MDX-Net-Dereverb模型挺慢的；<br>3、个人推荐的最干净的配置是先MDX-Net再DeEcho-Aggressive。": "使用UVR5模型進行人聲伴奏分離的批次處理。<br>有效資料夾路徑格式的例子：D:\\path\\to\\input\\folder（從檔案管理員地址欄複製）。<br>模型分為三類：<br>1. 保留人聲：選擇這個選項適用於沒有和聲的音訊。它比HP5更好地保留了人聲。它包括兩個內建模型：HP2和HP3。HP3可能輕微漏出伴奏，但比HP2更好地保留了人聲；<br>2. 僅保留主人聲：選擇這個選項適用於有和聲的音訊。它可能會削弱主人聲。它包括一個內建模型：HP5。<br>3. 消除混響和延遲模型（由FoxJoy提供）：<br>  (1) MDX-Net：對於立體聲混響的移除是最好的選擇，但不能移除單聲道混響；<br>&emsp;(234) DeEcho：移除延遲效果。Aggressive模式比Normal模式移除得更徹底。DeReverb另外移除混響，可以移除單聲道混響，但對於高頻重的板式混響移除不乾淨。<br>消除混響/延遲注意事項：<br>1. DeEcho-DeReverb模型的處理時間是其他兩個DeEcho模型的近兩倍；<br>2. MDX-Net-Dereverb模型相當慢；<br>3. 個人推薦的最乾淨配置是先使用MDX-Net，然後使用DeEcho-Aggressive。", "以-分隔输入使用的卡号, 例如   0-1-2   使用卡0和卡1和卡2": "以-分隔輸入使用的卡號, 例如 0-1-2 使用卡0和卡1和卡2", "伴奏人声分离&去混响&去回声": "伴奏人聲分離&去混響&去回聲", "使用模型采样率": "使用模型采样率", "使用设备采样率": "使用设备采样率", "保存名": "儲存名", "保存的文件名, 默认空为和源文件同名": "儲存的檔案名，預設空為與來源檔案同名", "保存的模型名不带后缀": "儲存的模型名不帶副檔名", "保存频率save_every_epoch": "保存頻率save_every_epoch", "保护清辅音和呼吸声，防止电音撕裂等artifact，拉满0.5不开启，调低加大保护力度但可能降低索引效果": "保護清輔音和呼吸聲，防止電音撕裂等artifact，拉滿0.5不開啟，調低加大保護力度但可能降低索引效果", "修改": "修改", "修改模型信息(仅支持weights文件夹下提取的小模型文件)": "修改模型資訊(僅支援weights資料夾下提取的小模型檔案)", "停止音频转换": "停止音訊轉換", "全流程结束！": "全流程结束！", "刷新音色列表和索引路径": "刷新音色列表和索引路徑", "加载模型": "載入模型", "加载预训练底模D路径": "加載預訓練底模D路徑", "加载预训练底模G路径": "加載預訓練底模G路徑", "单次推理": "单次推理", "卸载音色省显存": "卸載音色節省 VRAM", "变调(整数, 半音数量, 升八度12降八度-12)": "變調(整數、半音數量、升八度12降八度-12)", "后处理重采样至最终采样率，0为不进行重采样": "後處理重採樣至最終採樣率，0為不進行重採樣", "否": "否", "启用相位声码器": "启用相位声码器", "响应阈值": "響應閾值", "响度因子": "響度因子", "处理数据": "處理資料", "导出Onnx模型": "导出Onnx模型", "导出文件格式": "導出檔格式", "常见问题解答": "常見問題解答", "常规设置": "一般設定", "开始音频转换": "開始音訊轉換", "很遗憾您这没有能用的显卡来支持您训练": "很遗憾您这没有能用的显卡来支持您训练", "性能设置": "效能設定", "总训练轮数total_epoch": "總訓練輪數total_epoch", "批量推理": "批量推理", "批量转换, 输入待转换音频文件夹, 或上传多个音频文件, 在指定文件夹(默认opt)下输出转换的音频. ": "批量轉換，輸入待轉換音頻資料夾，或上傳多個音頻檔案，在指定資料夾(默認opt)下輸出轉換的音頻。", "指定输出主人声文件夹": "指定输出主人声文件夹", "指定输出文件夹": "指定輸出資料夾", "指定输出非主人声文件夹": "指定输出非主人声文件夹", "推理时间(ms):": "推理時間(ms):", "推理音色": "推理音色", "提取": "提取", "提取音高和处理数据使用的CPU进程数": "提取音高和處理數據使用的CPU進程數", "是": "是", "是否仅保存最新的ckpt文件以节省硬盘空间": "是否僅保存最新的ckpt檔案以節省硬碟空間", "是否在每次保存时间点将最终小模型保存至weights文件夹": "是否在每次保存時間點將最終小模型保存至weights檔夾", "是否缓存所有训练集至显存. 10min以下小数据可缓存以加速训练, 大数据缓存会炸显存也加不了多少速": "是否緩存所有訓練集至 VRAM。小於10分鐘的小數據可緩存以加速訓練，大數據緩存會爆 VRAM 也加不了多少速度", "显卡信息": "顯示卡資訊", "本软件以MIT协议开源, 作者不对软件具备任何控制力, 使用软件者、传播软件导出的声音者自负全责. <br>如不认可该条款, 则不能使用或引用软件包内任何代码和文件. 详见根目录<b>LICENSE</b>.": "本軟體以MIT協議開源，作者不對軟體具備任何控制力，使用軟體者、傳播軟體導出的聲音者自負全責。<br>如不認可該條款，則不能使用或引用軟體包內任何程式碼和檔案。詳見根目錄<b>使用需遵守的協議-LICENSE.txt</b>。", "查看": "查看", "查看模型信息(仅支持weights文件夹下提取的小模型文件)": "查看模型資訊(僅支援weights資料夾下提取的小模型檔案)", "检索特征占比": "檢索特徵佔比", "模型": "模型", "模型推理": "模型推理", "模型提取(输入logs文件夹下大文件模型路径),适用于训一半不想训了模型没有自动提取保存小文件模型,或者想测试中间模型的情况": "模型提取(輸入logs資料夾下大檔案模型路徑)，適用於訓一半不想訓了模型沒有自動提取儲存小檔案模型，或者想測試中間模型的情況", "模型是否带音高指导": "模型是否帶音高指導", "模型是否带音高指导(唱歌一定要, 语音可以不要)": "模型是否帶音高指導（唱歌一定要，語音可以不要）", "模型是否带音高指导,1是0否": "模型是否帶音高指導，1是0否", "模型版本型号": "模型版本型號", "模型融合, 可用于测试音色融合": "模型融合，可用於測試音色融合", "模型路径": "模型路徑", "每张显卡的batch_size": "每张显卡的batch_size", "淡入淡出长度": "淡入淡出長度", "版本": "版本", "特征提取": "特徵提取", "特征检索库文件路径,为空则使用下拉的选择结果": "特徵檢索庫檔路徑,為空則使用下拉的選擇結果", "男转女推荐+12key, 女转男推荐-12key, 如果音域爆炸导致音色失真也可以自己调整到合适音域. ": "男性轉女性推薦+12key，女性轉男性推薦-12key，如果音域爆炸導致音色失真也可以自己調整到合適音域。", "目标采样率": "目標取樣率", "算法延迟(ms):": "算法延迟(ms):", "自动检测index路径,下拉式选择(dropdown)": "自動檢測index路徑,下拉式選擇(dropdown)", "融合": "融合", "要改的模型信息": "要改的模型資訊", "要置入的模型信息": "要置入的模型資訊", "训练": "訓練", "训练模型": "訓練模型", "训练特征索引": "訓練特徵索引", "训练结束, 您可查看控制台训练日志或实验文件夹下的train.log": "训练结束, 您可查看控制台训练日志或实验文件夹下的train.log", "请指定说话人id": "請指定說話人id", "请选择index文件": "请选择index文件", "请选择pth文件": "请选择pth文件", "请选择说话人id": "請選擇說話人ID", "转换": "轉換", "输入实验名": "輸入實驗名稱", "输入待处理音频文件夹路径": "輸入待處理音頻資料夾路徑", "输入待处理音频文件夹路径(去文件管理器地址栏拷就行了)": "輸入待處理音頻資料夾路徑(去檔案管理器地址欄拷貝即可)", "输入待处理音频文件路径(默认是正确格式示例)": "輸入待處理音頻檔案路徑（預設是正確格式示例）", "输入源音量包络替换输出音量包络融合比例，越靠近1越使用输出包络": "輸入源音量包絡替換輸出音量包絡融合比例，越靠近1越使用輸出包絡", "输入监听": "输入监听", "输入训练文件夹路径": "輸入訓練檔案夾路徑", "输入设备": "輸入設備", "输入降噪": "輸入降噪", "输出信息": "輸出訊息", "输出变声": "输出变声", "输出设备": "輸出設備", "输出降噪": "輸出降噪", "输出音频(右下角三个点,点了可以下载)": "輸出音頻(右下角三個點，點了可以下載)", "选择.index文件": "選擇 .index 檔案", "选择.pth文件": "選擇 .pth 檔案", "选择音高提取算法,输入歌声可用pm提速,harvest低音好但巨慢无比,crepe效果好但吃GPU": "选择音高提取算法,输入歌声可用pm提速,harvest低音好但巨慢无比,crepe效果好但吃GPU", "选择音高提取算法,输入歌声可用pm提速,harvest低音好但巨慢无比,crepe效果好但吃GPU,rmvpe效果最好且微吃GPU": "選擇音高提取演算法,輸入歌聲可用pm提速,harvest低音好但巨慢無比,crepe效果好但吃GPU,rmvpe效果最好且微吃GPU", "选择音高提取算法:输入歌声可用pm提速,高质量语音但CPU差可用dio提速,harvest质量更好但慢,rmvpe效果最好且微吃CPU/GPU": "选择音高提取算法:输入歌声可用pm提速,高质量语音但CPU差可用dio提速,harvest质量更好但慢,rmvpe效果最好且微吃CPU/GPU", "采样率:": "采样率:", "采样长度": "取樣長度", "重载设备列表": "重載設備列表", "音调设置": "音調設定", "音频设备(请使用同种类驱动)": "音訊設備 (請使用同種類驅動)", "音高算法": "音高演算法", "额外推理时长": "額外推理時長"}