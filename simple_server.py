#!/usr/bin/env python3
"""
简化的测试服务器，用于验证基本环境和依赖
"""

from flask import Flask, request, jsonify
from flask_cors import CORS
import json
import os
import sys

app = Flask(__name__)
CORS(app)  # 允许跨域请求

@app.route('/test', methods=['GET'])
def test():
    """测试接口"""
    return jsonify({
        "status": "success",
        "message": "Digital Human API is running!",
        "python_version": sys.version,
        "environment": "Digital"
    })

@app.route('/Login', methods=['POST'])
def Login():
    """简化的登录接口"""
    try:
        POST_JSON = request.get_json()
        user_name = POST_JSON.get("User", "")
        user_password = POST_JSON.get("Password", "")
        
        # 简单的测试验证
        if user_name and user_password:
            return jsonify(result="Success")
        else:
            return jsonify(result="Failed")
    except Exception as e:
        return jsonify(result="Failed", error=str(e))

@app.route('/health', methods=['GET'])
def health():
    """健康检查接口"""
    try:
        # 检查基本依赖
        dependencies = {}
        errors = []
        
        try:
            import torch
            dependencies["torch"] = torch.__version__
            dependencies["cuda_available"] = torch.cuda.is_available()
            dependencies["gpu_count"] = torch.cuda.device_count() if torch.cuda.is_available() else 0
        except Exception as e:
            errors.append(f"torch: {str(e)}")
            
        try:
            import numpy
            dependencies["numpy"] = numpy.__version__
        except Exception as e:
            errors.append(f"numpy: {str(e)}")
            
        try:
            import cv2
            dependencies["opencv"] = cv2.__version__
        except Exception as e:
            errors.append(f"opencv: {str(e)}")
            
        try:
            import PIL
            dependencies["pillow"] = PIL.__version__
        except Exception as e:
            errors.append(f"pillow: {str(e)}")
            
        try:
            import flask
            dependencies["flask"] = flask.__version__
        except Exception as e:
            errors.append(f"flask: {str(e)}")
        
        return jsonify({
            "status": "healthy" if not errors else "partial",
            "dependencies": dependencies,
            "errors": errors
        })
    except Exception as e:
        return jsonify({
            "status": "error",
            "error": str(e)
        })

@app.route('/check_models', methods=['GET'])
def check_models():
    """检查模型文件是否存在"""
    try:
        model_paths = {
            "SadTalker_checkpoints": "SadTalker/checkpoints",
            "VITS_GPT_weights": "VITS/GPT_weights", 
            "VITS_SoVITS_weights": "VITS/SoVITS_weights",
            "Easy_Wav2Lip_checkpoints": "Easy_Wav2Lip/checkpoints"
        }
        
        model_status = {}
        for name, path in model_paths.items():
            if os.path.exists(path):
                files = os.listdir(path) if os.path.isdir(path) else []
                model_status[name] = {
                    "exists": True,
                    "files_count": len(files),
                    "files": files[:5]  # 只显示前5个文件
                }
            else:
                model_status[name] = {"exists": False}
        
        return jsonify({
            "status": "success",
            "models": model_status
        })
    except Exception as e:
        return jsonify({
            "status": "error", 
            "error": str(e)
        })

if __name__ == '__main__':
    print("Starting Digital Human API Test Server...")
    print("Server will run on http://0.0.0.0:5000")
    print("Test endpoints:")
    print("  GET  /test        - Basic test")
    print("  GET  /health      - Health check")
    print("  GET  /check_models - Check model files")
    print("  POST /Login       - Login test")
    
    app.run(host='0.0.0.0', port=5000, debug=True)
